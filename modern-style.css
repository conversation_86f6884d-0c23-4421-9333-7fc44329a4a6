/* CSS Variables */
:root {
    --primary-color: #e50914;
    --primary-hover: #f40612;
    --background-dark: #0a0a0a;
    --background-darker: #000000;
    --background-card: #1a1a1a;
    --text-white: #ffffff;
    --text-gray: #b3b3b3;
    --text-dark-gray: #8c8c8c;
    --border-gray: #333333;
    --gradient-overlay: linear-gradient(to right, rgba(0,0,0,0.8) 0%, rgba(0,0,0,0.4) 50%, rgba(0,0,0,0.1) 100%);
    --shadow-light: 0 2px 8px rgba(0,0,0,0.3);
    --shadow-heavy: 0 8px 32px rgba(0,0,0,0.6);
    --transition-fast: 0.3s ease;
    --transition-smooth: 0.5s ease;
    --border-radius: 8px;
    --border-radius-large: 12px;
}

/* Global Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Noto Sans Thai', sans-serif;
    background-color: var(--background-dark);
    color: var(--text-white);
    line-height: 1.6;
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Navbar Styles */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    background: rgba(10, 10, 10, 0.9);
    backdrop-filter: blur(10px);
    transition: var(--transition-fast);
    padding: 15px 0;
}

.navbar.scrolled {
    background: var(--background-darker);
    box-shadow: var(--shadow-light);
    padding: 10px 0;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-logo h2 {
    color: var(--primary-color);
    font-size: 28px;
    font-weight: 800;
    letter-spacing: -1px;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 30px;
}

.nav-link {
    color: var(--text-gray);
    text-decoration: none;
    font-weight: 500;
    transition: var(--transition-fast);
    position: relative;
    padding: 8px 0;
}

.nav-link:hover,
.nav-link.active {
    color: var(--text-white);
}

.nav-link.active::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--primary-color);
    border-radius: 2px;
}

.nav-icons {
    display: flex;
    gap: 15px;
}

.nav-icon-btn {
    background: none;
    border: none;
    color: var(--text-gray);
    font-size: 18px;
    cursor: pointer;
    padding: 8px;
    border-radius: 50%;
    transition: var(--transition-fast);
}

.nav-icon-btn:hover {
    color: var(--text-white);
    background: rgba(255, 255, 255, 0.1);
}

/* Hero Section */
.hero {
    position: relative;
    height: 100vh;
    min-height: 600px;
    display: flex;
    align-items: center;
    overflow: hidden;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -2;
}

.hero-background img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    filter: brightness(0.4) blur(1px);
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--gradient-overlay);
    z-index: -1;
}

.hero-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    z-index: 1;
}

.hero-info {
    max-width: 600px;
}

.hero-title {
    font-size: clamp(2.5rem, 5vw, 4rem);
    font-weight: 800;
    margin-bottom: 20px;
    line-height: 1.1;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
}

.hero-description {
    font-size: 18px;
    color: var(--text-gray);
    margin-bottom: 25px;
    line-height: 1.6;
    max-width: 500px;
}

.hero-tags {
    display: flex;
    gap: 10px;
    margin-bottom: 30px;
    flex-wrap: wrap;
}

.tag {
    background: rgba(229, 9, 20, 0.2);
    color: var(--primary-color);
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 500;
    border: 1px solid var(--primary-color);
}

.play-btn {
    background: var(--primary-color);
    color: var(--text-white);
    border: none;
    padding: 15px 30px;
    border-radius: var(--border-radius-large);
    font-size: 18px;
    font-weight: 600;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 12px;
    transition: var(--transition-fast);
    box-shadow: var(--shadow-light);
}

.play-btn:hover {
    background: var(--primary-hover);
    transform: translateY(-3px);
    box-shadow: var(--shadow-heavy);
}

.play-btn i {
    font-size: 16px;
}

/* Tab Categories */
.tab-categories {
    background: var(--background-darker);
    padding: 20px 0;
    border-bottom: 1px solid var(--border-gray);
}

.tabs {
    display: flex;
    gap: 30px;
    justify-content: center;
    flex-wrap: wrap;
}

.tab-btn {
    background: none;
    border: none;
    color: var(--text-gray);
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    padding: 12px 0;
    position: relative;
    transition: var(--transition-fast);
}

.tab-btn:hover,
.tab-btn.active {
    color: var(--text-white);
}

.tab-btn.active::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--primary-color);
    border-radius: 2px;
}

/* Main Content */
.main-content {
    padding: 60px 0;
}

.movie-section {
    margin-bottom: 60px;
}

.section-header {
    margin-bottom: 30px;
}

.section-title {
    font-size: 28px;
    font-weight: 700;
    color: var(--text-white);
}

/* Carousel Container */
.carousel-container {
    position: relative;
}

.movie-cards-wrapper {
    display: flex;
    gap: 20px;
    overflow-x: auto;
    scroll-behavior: smooth;
    padding: 10px 0;
    scrollbar-width: none;
    -ms-overflow-style: none;
}

.movie-cards-wrapper::-webkit-scrollbar {
    display: none;
}

/* Navigation Arrows */
.nav-arrow {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(0, 0, 0, 0.7);
    border: none;
    color: var(--text-white);
    width: 50px;
    height: 50px;
    border-radius: 50%;
    cursor: pointer;
    font-size: 18px;
    transition: var(--transition-fast);
    z-index: 10;
    backdrop-filter: blur(10px);
}

.nav-arrow:hover {
    background: var(--primary-color);
    transform: translateY(-50%) scale(1.1);
}

.nav-arrow-left {
    left: -25px;
}

.nav-arrow-right {
    right: -25px;
}

/* Movie Cards */
.movie-card {
    position: relative;
    min-width: 200px;
    height: 300px;
    border-radius: var(--border-radius-large);
    overflow: hidden;
    cursor: pointer;
    transition: var(--transition-smooth);
    background: var(--background-card);
}

.movie-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-heavy);
}

.movie-card img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition-smooth);
}

.movie-card:hover img {
    transform: scale(1.05);
}

.movie-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: var(--transition-fast);
}

.movie-card:hover .movie-overlay {
    opacity: 1;
}

.movie-play-icon {
    width: 60px;
    height: 60px;
    background: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-white);
    font-size: 24px;
    transition: var(--transition-fast);
}

.movie-play-icon:hover {
    background: var(--primary-hover);
    transform: scale(1.1);
}

.zoom-tag {
    position: absolute;
    top: 10px;
    left: 10px;
    background: #00ff00;
    color: #000;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 700;
    z-index: 5;
}

/* Top 10 Specific Styles */
.top-10-wrapper .movie-card {
    min-width: 250px;
    height: 350px;
}

.top-10-card {
    position: relative;
}

.rank-number {
    position: absolute;
    top: -10px;
    left: -10px;
    font-size: 120px;
    font-weight: 900;
    color: rgba(255, 255, 255, 0.1);
    -webkit-text-stroke: 2px var(--primary-color);
    z-index: 3;
    line-height: 1;
    pointer-events: none;
}

/* Footer */
.footer {
    background: var(--background-darker);
    padding: 40px 0;
    text-align: center;
    border-top: 1px solid var(--border-gray);
}

.view-all-btn {
    background: linear-gradient(45deg, var(--primary-color), #ff6b6b);
    color: var(--text-white);
    border: none;
    padding: 15px 30px;
    border-radius: var(--border-radius-large);
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 10px;
    transition: var(--transition-fast);
    box-shadow: var(--shadow-light);
}

.view-all-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-heavy);
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-menu {
        display: none;
    }

    .hero-title {
        font-size: 2rem;
    }

    .hero-description {
        font-size: 16px;
    }

    .tabs {
        gap: 15px;
    }

    .tab-btn {
        font-size: 14px;
    }

    .movie-card {
        min-width: 150px;
        height: 225px;
    }

    .top-10-wrapper .movie-card {
        min-width: 180px;
        height: 270px;
    }

    .nav-arrow {
        display: none;
    }

    .rank-number {
        font-size: 80px;
    }
}

/* Custom Scrollbar */
.movie-cards-wrapper::-webkit-scrollbar {
    height: 8px;
}

.movie-cards-wrapper::-webkit-scrollbar-track {
    background: var(--background-card);
    border-radius: 4px;
}

.movie-cards-wrapper::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 4px;
}

.movie-cards-wrapper::-webkit-scrollbar-thumb:hover {
    background: var(--primary-hover);
}

/* Additional Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.animate-on-scroll {
    animation: fadeInUp 0.6s ease forwards;
}

/* Loading Animation */
.loading {
    position: relative;
    overflow: hidden;
}

.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% { left: -100%; }
    100% { left: 100%; }
}
