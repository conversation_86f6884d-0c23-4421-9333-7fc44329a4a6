<?php
/**
 * DoMovie Theme Functions - Simple Version
 * 
 * @package DoMovie_Theme
 * @version 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define theme constants
define('DOOMOVIE_THEME_VERSION', '1.0.0');
define('DOOMOVIE_THEME_DIR', get_template_directory());
define('DOOMOVIE_THEME_URI', get_template_directory_uri());

// Theme setup
function doomovie_theme_setup() {
    // Add theme support
    add_theme_support('title-tag');
    add_theme_support('post-thumbnails');
    add_theme_support('html5', array('search-form', 'comment-form', 'comment-list', 'gallery', 'caption'));
    add_theme_support('custom-logo');
    
    // Register navigation menus
    register_nav_menus(array(
        'primary' => __('Primary Menu', 'doomovie'),
        'footer' => __('Footer Menu', 'doomovie'),
    ));
    
    // Add custom image sizes
    add_image_size('movie-poster', 300, 450, true);
    add_image_size('movie-thumbnail', 150, 225, true);
}
add_action('after_setup_theme', 'doomovie_theme_setup');

// Enqueue scripts and styles
function doomovie_enqueue_scripts() {
    // Enqueue styles
    wp_enqueue_style('doomovie-style', get_stylesheet_uri(), array(), DOOMOVIE_THEME_VERSION);

    // Enqueue Google Fonts
    wp_enqueue_style('doomovie-fonts', 'https://fonts.googleapis.com/css2?family=Noto+Sans+Thai:wght@300;400;500;600;700&family=Poppins:wght@300;400;500;600;700;800&display=swap', array(), null);

    // Enqueue Font Awesome
    wp_enqueue_style('font-awesome', 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css', array(), '6.4.0');

    // Enqueue scripts
    wp_enqueue_script('jquery');

    // Add inline JavaScript for Hero Slider
    $hero_js = "
    jQuery(document).ready(function($) {
        // Hero Slider Auto-play
        let currentSlide = 0;
        const slides = $('.hero-slide');
        const dots = $('.hero-dots .dot');
        const totalSlides = slides.length;
        let autoSlideInterval;

        // Initialize background images
        slides.each(function() {
            const bgUrl = $(this).data('bg');
            if (bgUrl) {
                $(this).find('.hero-bg').css('background-image', 'url(' + bgUrl + ')');
            }
        });

        function showSlide(index) {
            slides.removeClass('active');
            dots.removeClass('active');

            slides.eq(index).addClass('active');
            dots.eq(index).addClass('active');

            currentSlide = index;
        }

        function nextSlide() {
            const next = (currentSlide + 1) % totalSlides;
            showSlide(next);
        }

        function prevSlide() {
            const prev = (currentSlide - 1 + totalSlides) % totalSlides;
            showSlide(prev);
        }

        function startAutoSlide() {
            autoSlideInterval = setInterval(nextSlide, 5000); // 5 seconds
        }

        function stopAutoSlide() {
            clearInterval(autoSlideInterval);
        }

        // Navigation buttons
        $('.hero-nav.next').click(function() {
            stopAutoSlide();
            nextSlide();
            startAutoSlide();
        });

        $('.hero-nav.prev').click(function() {
            stopAutoSlide();
            prevSlide();
            startAutoSlide();
        });

        // Dots navigation
        dots.click(function() {
            const index = $(this).data('slide');
            stopAutoSlide();
            showSlide(index);
            startAutoSlide();
        });

        // Pause on hover
        $('.hero-section').hover(
            function() { stopAutoSlide(); },
            function() { startAutoSlide(); }
        );

        // Start auto-slide
        startAutoSlide();

        // Navbar Scroll Effect
        $(window).scroll(function() {
            if ($(window).scrollTop() > 100) {
                $('.site-header').addClass('scrolled');
            } else {
                $('.site-header').removeClass('scrolled');
            }
        });

        // Tab Switching
        $('.tab-btn').click(function() {
            // Remove active class from all buttons
            $('.tab-btn').removeClass('active');
            // Add active class to clicked button
            $(this).addClass('active');

            // Here you can add logic to filter content based on selected tab
            const tabType = $(this).attr('data-tab');
            console.log('Selected tab:', tabType);
        });

        // Search toggle
        $('.search-button').click(function() {
            $('.search-form-container').slideToggle();
            $('.search-field').focus();
        });

        // Search close
        $('.search-close').click(function() {
            $('.search-form-container').slideUp();
        });

        // Close search on escape key
        $(document).keyup(function(e) {
            if (e.keyCode === 27) { // Escape key
                $('.search-form-container').slideUp();
            }
        });

        // Top 10 Auto Slider with pause on hover
        let top10CurrentSlide = 0;
        const top10Slides = $('.top-10-slide');
        const top10TotalSlides = top10Slides.length;
        let top10AutoSlideInterval;

        function showTop10Slide(index) {
            top10Slides.removeClass('active');
            top10Slides.eq(index).addClass('active');
            top10CurrentSlide = index;
        }

        function nextTop10Slide() {
            const next = (top10CurrentSlide + 1) % top10TotalSlides;
            showTop10Slide(next);
        }

        function startTop10AutoSlide() {
            top10AutoSlideInterval = setInterval(nextTop10Slide, 5000); // 5 seconds
        }

        function stopTop10AutoSlide() {
            clearInterval(top10AutoSlideInterval);
        }

        // Start Top 10 auto-slide
        if (top10TotalSlides > 1) {
            startTop10AutoSlide();

            // Pause on hover
            $('.top-10-section').hover(
                function() { stopTop10AutoSlide(); },
                function() { startTop10AutoSlide(); }
            );
        }

        // Movie Section Slider (6 movies visible layout)
        $('.movie-nav-arrow').click(function() {
            const target = $(this).data('target');
            const isNext = $(this).hasClass('next');
            const slider = $('#' + target + '-slider .movies-grid');
            const movieCards = slider.find('.movie-card');
            const visibleCards = 6;
            const totalCards = movieCards.length;

            if (isNext && totalCards > visibleCards) {
                // Show hidden cards and hide first 6
                movieCards.slice(0, visibleCards).addClass('movie-card-hidden');
                movieCards.slice(visibleCards).removeClass('movie-card-hidden');
                $(this).prop('disabled', true);
                $(this).siblings('.prev').prop('disabled', false);
            } else if (!isNext) {
                // Show first 6 cards and hide the rest
                movieCards.removeClass('movie-card-hidden');
                movieCards.slice(visibleCards).addClass('movie-card-hidden');
                $(this).prop('disabled', true);
                $(this).siblings('.next').prop('disabled', false);
            }
        });

        // Initialize button states for movie sliders
        $('.movie-section-slider').each(function() {
            const movieCards = $(this).find('.movie-card');
            const visibleCards = 6;

            // Hide cards beyond the first 6
            movieCards.slice(visibleCards).addClass('movie-card-hidden');

            // Set initial button states
            $(this).find('.movie-nav-arrow.prev').prop('disabled', true);
            $(this).find('.movie-nav-arrow.next').prop('disabled', movieCards.length <= visibleCards);
        });

        // Smooth scrolling for anchor links
        $('a[href^=\"#\"]').click(function(e) {
            e.preventDefault();
            const target = $(this.getAttribute('href'));
            if (target.length) {
                $('html, body').animate({
                    scrollTop: target.offset().top - 70 // Account for fixed header
                }, 500);
            }
        });

        // Back to top button
        const backToTopBtn = $('#back-to-top');

        $(window).scroll(function() {
            if ($(this).scrollTop() > 300) {
                backToTopBtn.fadeIn();
            } else {
                backToTopBtn.fadeOut();
            }
        });

        backToTopBtn.click(function() {
            $('html, body').animate({scrollTop: 0}, 500);
        });

        // Loading states for movie cards
        $('.movie-card img').on('load', function() {
            $(this).closest('.movie-card').addClass('loaded');
        });

        // Add loading animation to movie posters
        $('.movie-poster-img').each(function() {
            if (!this.complete) {
                $(this).closest('.movie-card').addClass('loading');
            }
        });

        $('.movie-poster-img').on('load', function() {
            $(this).closest('.movie-card').removeClass('loading').addClass('loaded');
        });

        // Enhanced hover effects for movie cards
        $('.movie-card').hover(
            function() {
                $(this).addClass('hovered');
            },
            function() {
                $(this).removeClass('hovered');
            }
        );

        // Carousel Navigation
        $('.nav-arrow').click(function() {
            const direction = $(this).attr('data-direction');
            const wrapper = $(this).siblings('.movie-cards-wrapper');

            if (direction === 'left') {
                wrapper.animate({
                    scrollLeft: wrapper.scrollLeft() - 300
                }, 300);
            } else {
                wrapper.animate({
                    scrollLeft: wrapper.scrollLeft() + 300
                }, 300);
            }
        });

        // Doomovie Movie Data - Based on EZmovie.me
        var movieData = {
            recommended: [
                {title: \"Superman (2025)\", image: \"https://ezmovie.me/media/cache/strip/202507/block/eacf81bbc9a770e457bb16b7004a92e5.png\"},
                {title: \"ปากกัด ตีนถีบ (2025)\", image: \"https://ezmovie.me/media/cache/strip/202507/block/5e81659fc61cb71daf4c578db29b9a2c.png\"},
                {title: \"Jurassic World Rebirth (2025)\", image: \"https://ezmovie.me/media/cache/strip/202507/block/03794c7927a655afde43650456749704.png\"},
                {title: \"Squid Game 3 (2025)\", image: \"https://ezmovie.me/media/cache/strip/202506/block/f51b5a1b5465de177173b92637bd2d26.png\"},
                {title: \"Ironheart (2025)\", image: \"https://ezmovie.me/media/cache/strip/202506/block/406c562c07c0c3e8f169758e20c4cd72.png\"},
                {title: \"F1 The Movie (2025)\", image: \"https://ezmovie.me/media/cache/strip/202506/block/f5e774dccc8757022019e5ca13d59535.png\"},
                {title: \"How to Train Your Dragon (2025)\", image: \"https://ezmovie.me/media/cache/strip/202506/block/614d59bbb8e842c302564008053ea1bf.png\"},
                {title: \"28 Years Later (2025)\", image: \"https://ezmovie.me/media/cache/strip/202506/block/9e1276075341d40a1cfb1d69aaba6259.png\"}
            ],
            newMovies: [
                {title: \"Lilo & Stitch (2025)\", image: \"https://ezmovie.me/media/cache/strip/202505/block/69bf3ffc002b2f8c341260059cd417e2.png\"},
                {title: \"M3GAN 2.0 (2025)\", image: \"https://ezmovie.me/media/cache/strip/202506/block/406c562c07c0c3e8f169758e20c4cd72.png\"},
                {title: \"Ballerina (2025)\", image: \"https://ezmovie.me/media/cache/strip/202506/block/9e1276075341d40a1cfb1d69aaba6259.png\"},
                {title: \"Karate Kid Legends (2025)\", image: \"https://ezmovie.me/media/cache/strip/202506/block/c9bdf00bef2b3bc52b4778255c34aa65.png\"},
                {title: \"Captain America (2025)\", image: \"https://ezmovie.me/media/cache/strip/202507/block/03794c7927a655afde43650456749704.png\"},
                {title: \"Fantastic Four (2025)\", image: \"https://ezmovie.me/media/cache/strip/202506/block/f5e774dccc8757022019e5ca13d59535.png\"},
                {title: \"Blade (2025)\", image: \"https://ezmovie.me/media/cache/strip/202506/block/9e1276075341d40a1cfb1d69aaba6259.png\"},
                {title: \"Deadpool 4 (2025)\", image: \"https://ezmovie.me/media/cache/strip/202505/block/69bf3ffc002b2f8c341260059cd417e2.png\"}
            ],
            top10: [
                {title: \"ปากกัด ตีนถีบ (2025)\", image: \"https://ezmovie.me/media/cache/strip/202507/block/5e81659fc61cb71daf4c578db29b9a2c.png\", rank: 1},
                {title: \"Squid Game 3 (2025)\", image: \"https://ezmovie.me/media/cache/strip/202506/block/f51b5a1b5465de177173b92637bd2d26.png\", rank: 2},
                {title: \"The Old Guard 2 (2025)\", image: \"https://ezmovie.me/media/cache/strip/202507/block/03794c7927a655afde43650456749704.png\", rank: 3},
                {title: \"Ironheart (2025)\", image: \"https://ezmovie.me/media/cache/strip/202506/block/406c562c07c0c3e8f169758e20c4cd72.png\", rank: 4},
                {title: \"Ghost Rider (2007)\", image: \"https://ezmovie.me/media/cache/strip/202506/block/9e1276075341d40a1cfb1d69aaba6259.png\", rank: 5},
                {title: \"สงคราม ส่งด่วน (2025)\", image: \"https://ezmovie.me/media/cache/strip/202506/block/f5e774dccc8757022019e5ca13d59535.png\", rank: 6},
                {title: \"Captain America (2025)\", image: \"https://ezmovie.me/media/cache/strip/202506/block/614d59bbb8e842c302564008053ea1bf.png\", rank: 7},
                {title: \"Mercy For None (2025)\", image: \"https://ezmovie.me/media/cache/strip/202505/block/69bf3ffc002b2f8c341260059cd417e2.png\", rank: 8},
                {title: \"The Gorge (2025)\", image: \"https://ezmovie.me/media/cache/strip/202506/block/c9bdf00bef2b3bc52b4778255c34aa65.png\", rank: 9},
                {title: \"FUBAR 2 (2025)\", image: \"https://ezmovie.me/media/cache/strip/202507/block/03794c7927a655afde43650456749704.png\", rank: 10}
            ]
        };

        // Create Movie Card Function
        function createMovieCard(movie, isTop10) {
            isTop10 = isTop10 || false;
            var cardHtml = '<div class=\"movie-card' + (isTop10 ? ' top-10-card' : '') + '\">';
            cardHtml += '<a href=\"movie.php?title=' + encodeURIComponent(movie.title) + '&image=' + encodeURIComponent(movie.image) + '\">';
            cardHtml += '<img src=\"' + movie.image + '\" alt=\"' + movie.title + '\" loading=\"lazy\">';
            cardHtml += '<div class=\"movie-overlay\">';
            cardHtml += '<div class=\"movie-play-icon\"><i class=\"fas fa-play\"></i></div>';
            cardHtml += '</div>';
            cardHtml += '<div class=\"zoom-tag\">ZOOM</div>';
            if (isTop10) {
                cardHtml += '<div class=\"rank-number\">' + movie.rank + '</div>';
            }
            cardHtml += '</a>';
            cardHtml += '</div>';
            return cardHtml;
        }

        // Populate Movie Sections
        function populateMovieSections() {
            // Recommended Movies
            var recommendedHtml = '';
            for (var i = 0; i < movieData.recommended.length; i++) {
                recommendedHtml += createMovieCard(movieData.recommended[i]);
            }
            $('#recommended-movies').html(recommendedHtml);

            // New Movies
            var newMoviesHtml = '';
            for (var i = 0; i < movieData.newMovies.length; i++) {
                newMoviesHtml += createMovieCard(movieData.newMovies[i]);
            }
            $('#new-movies').html(newMoviesHtml);

            // Top 10 Movies
            var top10Html = '';
            for (var i = 0; i < movieData.top10.length; i++) {
                top10Html += createMovieCard(movieData.top10[i], true);
            }
            $('#top-10-movies').html(top10Html);
        }

        // Initialize Movie Sections
        populateMovieSections();

        // Keyboard navigation for carousel
        $(document).keydown(function(e) {
            if (e.keyCode === 37) { // Left arrow
                $('.nav-arrow[data-direction=\"left\"]:visible').first().click();
            } else if (e.keyCode === 39) { // Right arrow
                $('.nav-arrow[data-direction=\"right\"]:visible').first().click();
            }
        });
    });
    ";
    wp_add_inline_script('jquery', $hero_js);

    // Add inline styles for immediate loading
    $custom_css = "
        body {
            font-family: 'Noto Sans Thai', 'Helvetica Neue', Arial, sans-serif !important;
            background-color: #0a0a0a !important;
            color: #ffffff !important;
        }
        .site-header {
            background: rgba(10, 10, 10, 0.95) !important;
            border-bottom: 1px solid #333333 !important;
        }
    ";
    wp_add_inline_style('doomovie-style', $custom_css);
}
add_action('wp_enqueue_scripts', 'doomovie_enqueue_scripts');

// Register widget areas
function doomovie_widgets_init() {
    register_sidebar(array(
        'name'          => __('Sidebar', 'doomovie'),
        'id'            => 'sidebar-1',
        'description'   => __('Add widgets here.', 'doomovie'),
        'before_widget' => '<section id="%1$s" class="widget %2$s">',
        'after_widget'  => '</section>',
        'before_title'  => '<h2 class="widget-title">',
        'after_title'   => '</h2>',
    ));
}
add_action('widgets_init', 'doomovie_widgets_init');

// Custom excerpt length
function doomovie_excerpt_length($length) {
    return 20;
}
add_filter('excerpt_length', 'doomovie_excerpt_length');

// Custom excerpt more
function doomovie_excerpt_more($more) {
    return '...';
}
add_filter('excerpt_more', 'doomovie_excerpt_more');
