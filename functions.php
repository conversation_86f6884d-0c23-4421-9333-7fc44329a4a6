<?php
/**
 * DoMovie Theme Functions - Simple Version
 * 
 * @package DoMovie_Theme
 * @version 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define theme constants
define('DOOMOVIE_THEME_VERSION', '1.0.0');
define('DOOMOVIE_THEME_DIR', get_template_directory());
define('DOOMOVIE_THEME_URI', get_template_directory_uri());

// Theme setup
function doomovie_theme_setup() {
    // Add theme support
    add_theme_support('title-tag');
    add_theme_support('post-thumbnails');
    add_theme_support('html5', array('search-form', 'comment-form', 'comment-list', 'gallery', 'caption'));
    add_theme_support('custom-logo');
    
    // Register navigation menus
    register_nav_menus(array(
        'primary' => __('Primary Menu', 'doomovie'),
        'footer' => __('Footer Menu', 'doomovie'),
    ));
    
    // Add custom image sizes
    add_image_size('movie-poster', 300, 450, true);
    add_image_size('movie-thumbnail', 150, 225, true);
}
add_action('after_setup_theme', 'doomovie_theme_setup');

// Enqueue scripts and styles
function doomovie_enqueue_scripts() {
    // Enqueue styles
    wp_enqueue_style('doomovie-style', get_stylesheet_uri(), array(), DOOMOVIE_THEME_VERSION);

    // Enqueue Google Fonts
    wp_enqueue_style('doomovie-fonts', 'https://fonts.googleapis.com/css2?family=Noto+Sans+Thai:wght@300;400;500;600;700&family=Poppins:wght@300;400;500;600;700;800&display=swap', array(), null);

    // Enqueue Font Awesome
    wp_enqueue_style('font-awesome', 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css', array(), '6.4.0');

    // Enqueue scripts
    wp_enqueue_script('jquery');

    // Add inline JavaScript for Hero Slider
    $hero_js = "
    jQuery(document).ready(function($) {
        // Hero Slider Auto-play
        let currentSlide = 0;
        const slides = $('.hero-slide');
        const dots = $('.hero-dots .dot');
        const totalSlides = slides.length;
        let autoSlideInterval;

        // Initialize background images
        slides.each(function() {
            const bgUrl = $(this).data('bg');
            if (bgUrl) {
                $(this).find('.hero-bg').css('background-image', 'url(' + bgUrl + ')');
            }
        });

        function showSlide(index) {
            slides.removeClass('active');
            dots.removeClass('active');

            slides.eq(index).addClass('active');
            dots.eq(index).addClass('active');

            currentSlide = index;
        }

        function nextSlide() {
            const next = (currentSlide + 1) % totalSlides;
            showSlide(next);
        }

        function prevSlide() {
            const prev = (currentSlide - 1 + totalSlides) % totalSlides;
            showSlide(prev);
        }

        function startAutoSlide() {
            autoSlideInterval = setInterval(nextSlide, 5000); // 5 seconds
        }

        function stopAutoSlide() {
            clearInterval(autoSlideInterval);
        }

        // Navigation buttons
        $('.hero-nav.next').click(function() {
            stopAutoSlide();
            nextSlide();
            startAutoSlide();
        });

        $('.hero-nav.prev').click(function() {
            stopAutoSlide();
            prevSlide();
            startAutoSlide();
        });

        // Dots navigation
        dots.click(function() {
            const index = $(this).data('slide');
            stopAutoSlide();
            showSlide(index);
            startAutoSlide();
        });

        // Pause on hover
        $('.hero-section').hover(
            function() { stopAutoSlide(); },
            function() { startAutoSlide(); }
        );

        // Start auto-slide
        startAutoSlide();

        // Search toggle
        $('.search-button').click(function() {
            $('.search-form-container').slideToggle();
            $('.search-field').focus();
        });

        // Search close
        $('.search-close').click(function() {
            $('.search-form-container').slideUp();
        });

        // Close search on escape key
        $(document).keyup(function(e) {
            if (e.keyCode === 27) { // Escape key
                $('.search-form-container').slideUp();
            }
        });

        // Top 10 Auto Slider with pause on hover
        let top10CurrentSlide = 0;
        const top10Slides = $('.top-10-slide');
        const top10TotalSlides = top10Slides.length;
        let top10AutoSlideInterval;

        function showTop10Slide(index) {
            top10Slides.removeClass('active');
            top10Slides.eq(index).addClass('active');
            top10CurrentSlide = index;
        }

        function nextTop10Slide() {
            const next = (top10CurrentSlide + 1) % top10TotalSlides;
            showTop10Slide(next);
        }

        function startTop10AutoSlide() {
            top10AutoSlideInterval = setInterval(nextTop10Slide, 5000); // 5 seconds
        }

        function stopTop10AutoSlide() {
            clearInterval(top10AutoSlideInterval);
        }

        // Start Top 10 auto-slide
        if (top10TotalSlides > 1) {
            startTop10AutoSlide();

            // Pause on hover
            $('.top-10-section').hover(
                function() { stopTop10AutoSlide(); },
                function() { startTop10AutoSlide(); }
            );
        }

        // Movie Section Slider (6 movies visible layout)
        $('.movie-nav-arrow').click(function() {
            const target = $(this).data('target');
            const isNext = $(this).hasClass('next');
            const slider = $('#' + target + '-slider .movies-grid');
            const movieCards = slider.find('.movie-card');
            const visibleCards = 6;
            const totalCards = movieCards.length;

            if (isNext && totalCards > visibleCards) {
                // Show hidden cards and hide first 6
                movieCards.slice(0, visibleCards).addClass('movie-card-hidden');
                movieCards.slice(visibleCards).removeClass('movie-card-hidden');
                $(this).prop('disabled', true);
                $(this).siblings('.prev').prop('disabled', false);
            } else if (!isNext) {
                // Show first 6 cards and hide the rest
                movieCards.removeClass('movie-card-hidden');
                movieCards.slice(visibleCards).addClass('movie-card-hidden');
                $(this).prop('disabled', true);
                $(this).siblings('.next').prop('disabled', false);
            }
        });

        // Initialize button states for movie sliders
        $('.movie-section-slider').each(function() {
            const movieCards = $(this).find('.movie-card');
            const visibleCards = 6;

            // Hide cards beyond the first 6
            movieCards.slice(visibleCards).addClass('movie-card-hidden');

            // Set initial button states
            $(this).find('.movie-nav-arrow.prev').prop('disabled', true);
            $(this).find('.movie-nav-arrow.next').prop('disabled', movieCards.length <= visibleCards);
        });

        // Smooth scrolling for anchor links
        $('a[href^="#"]').click(function(e) {
            e.preventDefault();
            const target = $(this.getAttribute('href'));
            if (target.length) {
                $('html, body').animate({
                    scrollTop: target.offset().top - 70 // Account for fixed header
                }, 500);
            }
        });

        // Back to top button
        const backToTopBtn = $('#back-to-top');

        $(window).scroll(function() {
            if ($(this).scrollTop() > 300) {
                backToTopBtn.fadeIn();
            } else {
                backToTopBtn.fadeOut();
            }
        });

        backToTopBtn.click(function() {
            $('html, body').animate({scrollTop: 0}, 500);
        });

        // Loading states for movie cards
        $('.movie-card img').on('load', function() {
            $(this).closest('.movie-card').addClass('loaded');
        });

        // Add loading animation to movie posters
        $('.movie-poster-img').each(function() {
            if (!this.complete) {
                $(this).closest('.movie-card').addClass('loading');
            }
        });

        $('.movie-poster-img').on('load', function() {
            $(this).closest('.movie-card').removeClass('loading').addClass('loaded');
        });

        // Enhanced hover effects for movie cards
        $('.movie-card').hover(
            function() {
                $(this).addClass('hovered');
            },
            function() {
                $(this).removeClass('hovered');
            }
        );

        // Keyboard navigation for hero slider
        $(document).keydown(function(e) {
            if (e.keyCode === 37) { // Left arrow
                $('.hero-nav.prev').click();
            } else if (e.keyCode === 39) { // Right arrow
                $('.hero-nav.next').click();
            }
        });
    });
    ";
    wp_add_inline_script('jquery', $hero_js);

    // Add inline styles for immediate loading
    $custom_css = "
        body {
            font-family: 'Noto Sans Thai', 'Helvetica Neue', Arial, sans-serif !important;
            background-color: #0a0a0a !important;
            color: #ffffff !important;
        }
        .site-header {
            background: rgba(10, 10, 10, 0.95) !important;
            border-bottom: 1px solid #333333 !important;
        }
    ";
    wp_add_inline_style('doomovie-style', $custom_css);
}
add_action('wp_enqueue_scripts', 'doomovie_enqueue_scripts');

// Register widget areas
function doomovie_widgets_init() {
    register_sidebar(array(
        'name'          => __('Sidebar', 'doomovie'),
        'id'            => 'sidebar-1',
        'description'   => __('Add widgets here.', 'doomovie'),
        'before_widget' => '<section id="%1$s" class="widget %2$s">',
        'after_widget'  => '</section>',
        'before_title'  => '<h2 class="widget-title">',
        'after_title'   => '</h2>',
    ));
}
add_action('widgets_init', 'doomovie_widgets_init');

// Custom excerpt length
function doomovie_excerpt_length($length) {
    return 20;
}
add_filter('excerpt_length', 'doomovie_excerpt_length');

// Custom excerpt more
function doomovie_excerpt_more($more) {
    return '...';
}
add_filter('excerpt_more', 'doomovie_excerpt_more');
