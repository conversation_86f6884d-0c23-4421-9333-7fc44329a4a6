<?php
/**
 * DoMovie Theme Functions - Simple Version
 * 
 * @package DoMovie_Theme
 * @version 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define theme constants
define('DOOMOVIE_THEME_VERSION', '1.0.0');
define('DOOMOVIE_THEME_DIR', get_template_directory());
define('DOOMOVIE_THEME_URI', get_template_directory_uri());

// Theme setup
function doomovie_theme_setup() {
    // Add theme support
    add_theme_support('title-tag');
    add_theme_support('post-thumbnails');
    add_theme_support('html5', array('search-form', 'comment-form', 'comment-list', 'gallery', 'caption'));
    add_theme_support('custom-logo');
    
    // Register navigation menus
    register_nav_menus(array(
        'primary' => __('Primary Menu', 'doomovie'),
        'footer' => __('Footer Menu', 'doomovie'),
    ));
    
    // Add custom image sizes
    add_image_size('movie-poster', 300, 450, true);
    add_image_size('movie-thumbnail', 150, 225, true);
}
add_action('after_setup_theme', 'doomovie_theme_setup');

// Enqueue scripts and styles
function doomovie_enqueue_scripts() {
    // Enqueue styles
    wp_enqueue_style('doomovie-style', get_stylesheet_uri(), array(), DOOMOVIE_THEME_VERSION);

    // Enqueue Google Fonts
    wp_enqueue_style('doomovie-fonts', 'https://fonts.googleapis.com/css2?family=Noto+Sans+Thai:wght@300;400;500;600;700&family=Poppins:wght@300;400;500;600;700;800&display=swap', array(), null);

    // Enqueue Font Awesome
    wp_enqueue_style('font-awesome', 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css', array(), '6.4.0');

    // Enqueue scripts
    wp_enqueue_script('jquery');

    // Add inline JavaScript for Hero Slider
    $hero_js = "
    jQuery(document).ready(function($) {
        // Hero Slider Auto-play
        let currentSlide = 0;
        const slides = $('.hero-slide');
        const dots = $('.hero-dots .dot');
        const totalSlides = slides.length;
        let autoSlideInterval;

        // Initialize background images
        slides.each(function() {
            const bgUrl = $(this).data('bg');
            if (bgUrl) {
                $(this).find('.hero-bg').css('background-image', 'url(' + bgUrl + ')');
            }
        });

        function showSlide(index) {
            slides.removeClass('active');
            dots.removeClass('active');

            slides.eq(index).addClass('active');
            dots.eq(index).addClass('active');

            currentSlide = index;
        }

        function nextSlide() {
            const next = (currentSlide + 1) % totalSlides;
            showSlide(next);
        }

        function prevSlide() {
            const prev = (currentSlide - 1 + totalSlides) % totalSlides;
            showSlide(prev);
        }

        function startAutoSlide() {
            autoSlideInterval = setInterval(nextSlide, 5000); // 5 seconds
        }

        function stopAutoSlide() {
            clearInterval(autoSlideInterval);
        }

        // Navigation buttons
        $('.hero-nav.next').click(function() {
            stopAutoSlide();
            nextSlide();
            startAutoSlide();
        });

        $('.hero-nav.prev').click(function() {
            stopAutoSlide();
            prevSlide();
            startAutoSlide();
        });

        // Dots navigation
        dots.click(function() {
            const index = $(this).data('slide');
            stopAutoSlide();
            showSlide(index);
            startAutoSlide();
        });

        // Pause on hover
        $('.hero-section').hover(
            function() { stopAutoSlide(); },
            function() { startAutoSlide(); }
        );

        // Start auto-slide
        startAutoSlide();

        // Search toggle
        $('.search-button').click(function() {
            $('.search-form-container').slideToggle();
        });

        // Top 10 Auto Slider - No manual controls needed
        // Auto animation is handled by CSS

        // Movie Section Slider (6.5 columns layout)
        $('.movie-nav-arrow').click(function() {
            const target = $(this).data('target');
            const isNext = $(this).hasClass('next');
            const slider = $('#' + target + '-slider .movies-grid');

            if (isNext) {
                // Slide to show next 6 movies (with 7th half visible)
                slider.css('transform', 'translateX(-50%)');
                $(this).prop('disabled', true);
                $(this).siblings('.prev').prop('disabled', false);
            } else {
                // Slide back to show first 6 movies (with 7th half visible)
                slider.css('transform', 'translateX(0%)');
                $(this).prop('disabled', true);
                $(this).siblings('.next').prop('disabled', false);
            }
        });

        // Initialize button states for movie sliders
        $('.movie-section-slider').each(function() {
            $(this).find('.movie-nav-arrow.prev').prop('disabled', true);
            $(this).find('.movie-nav-arrow.next').prop('disabled', false);
        });
    });
    ";
    wp_add_inline_script('jquery', $hero_js);

    // Add inline styles for immediate loading
    $custom_css = "
        body {
            font-family: 'Noto Sans Thai', 'Helvetica Neue', Arial, sans-serif !important;
            background-color: #0a0a0a !important;
            color: #ffffff !important;
        }
        .site-header {
            background: rgba(10, 10, 10, 0.95) !important;
            border-bottom: 1px solid #333333 !important;
        }
    ";
    wp_add_inline_style('doomovie-style', $custom_css);
}
add_action('wp_enqueue_scripts', 'doomovie_enqueue_scripts');

// Register widget areas
function doomovie_widgets_init() {
    register_sidebar(array(
        'name'          => __('Sidebar', 'doomovie'),
        'id'            => 'sidebar-1',
        'description'   => __('Add widgets here.', 'doomovie'),
        'before_widget' => '<section id="%1$s" class="widget %2$s">',
        'after_widget'  => '</section>',
        'before_title'  => '<h2 class="widget-title">',
        'after_title'   => '</h2>',
    ));
}
add_action('widgets_init', 'doomovie_widgets_init');

// Custom excerpt length
function doomovie_excerpt_length($length) {
    return 20;
}
add_filter('excerpt_length', 'doomovie_excerpt_length');

// Custom excerpt more
function doomovie_excerpt_more($more) {
    return '...';
}
add_filter('excerpt_more', 'doomovie_excerpt_more');
