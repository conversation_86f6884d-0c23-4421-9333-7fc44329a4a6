<?php
/**
 * The main template file - EZMovie Style
 *
 * @package DoMovie_Theme
 * @version 1.0.0
 */

get_header(); ?>

<main id="main" class="site-main">

    <?php if (is_home() || is_front_page()) : ?>

        <!-- Hero Slider Section - Full Width EZMovie Style -->
        <section class="hero-section">
            <div class="hero-slider">
                <!-- Slide 1: Superman -->
                <div class="hero-slide active" data-bg="https://ezmovie.me/media/cache/strip/202507/block/eacf81bbc9a770e457bb16b7004a92e5.png">
                    <div class="hero-bg"></div>
                    <div class="hero-overlay"></div>
                    <div class="container">
                        <div class="hero-content">
                            <div class="hero-meta">
                                <span class="hero-year">2025</span>
                                <span class="hero-rating">
                                    <img src="<?php echo get_template_directory_uri(); ?>/assets/images/rate-13+.png" alt="13+" class="rating-badge">
                                </span>
                                <span class="hero-duration">2 ชม. 15 นาที</span>
                                <span class="hero-quality">
                                    <img src="<?php echo get_template_directory_uri(); ?>/assets/images/hd-badge.png" alt="HD" class="quality-badge">
                                </span>
                            </div>
                            <h1 class="hero-title">Superman (2025) ซูเปอร์แมน</h1>
                            <p class="hero-description">ความหวังครั้งใหม่ ซูเปอร์แมน ของ เจมส์ กันน์ ที่จะพาทุกคนย้อนกลับไป สู่จุดที่เหล่าซูเปอร์ฮีโร่สร้างความหวัง และเขายังคงมีความเป็นมนุษย์อยู่</p>
                            <div class="hero-buttons">
                                <a href="#" class="btn btn-primary hero-watch-btn">
                                    <i class="fas fa-play"></i> ดูหนัง
                                </a>
                                <a href="#" class="btn btn-secondary hero-list-btn">
                                    <i class="fas fa-bookmark"></i> รายการของฉัน
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Slide 2: ปากกัด ตีนถีบ -->
                <div class="hero-slide" data-bg="https://ezmovie.me/media/cache/strip/202507/block/5e81659fc61cb71daf4c578db29b9a2c.png">
                    <div class="hero-bg"></div>
                    <div class="hero-overlay"></div>
                    <div class="container">
                        <div class="hero-content">
                            <div class="hero-meta">
                                <span class="hero-year">2025</span>
                                <span class="hero-rating">
                                    <img src="<?php echo get_template_directory_uri(); ?>/assets/images/rate-18+.png" alt="18+" class="rating-badge">
                                </span>
                                <span class="hero-duration">1 ชม. 45 นาที</span>
                                <span class="hero-quality">
                                    <img src="<?php echo get_template_directory_uri(); ?>/assets/images/hd-badge.png" alt="HD" class="quality-badge">
                                </span>
                            </div>
                            <h1 class="hero-title">ปากกัด ตีนถีบ (2025) Ziam</h1>
                            <p class="hero-description">อดีตยอดนักสู้มวยไทย หมาก ปริญ ต้องอาศัยทักษะ ความว่องไว และจิตใจทรหด ในสมรภูมิแห่งการต่อสู้เพื่อเอาชีวิตรอดจากฝูงซอมบี้มรณะ</p>
                            <div class="hero-buttons">
                                <a href="#" class="btn btn-primary hero-watch-btn">
                                    <i class="fas fa-play"></i> ดูหนัง
                                </a>
                                <a href="#" class="btn btn-secondary hero-list-btn">
                                    <i class="fas fa-bookmark"></i> รายการของฉัน
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Slide 3: Jurassic World Rebirth -->
                <div class="hero-slide" data-bg="https://ezmovie.me/media/cache/strip/202507/block/03794c7927a655afde43650456749704.png">
                    <div class="hero-bg"></div>
                    <div class="hero-overlay"></div>
                    <div class="container">
                        <div class="hero-content">
                            <div class="hero-meta">
                                <span class="hero-year">2025</span>
                                <span class="hero-rating">
                                    <img src="<?php echo get_template_directory_uri(); ?>/assets/images/rate-13+.png" alt="13+" class="rating-badge">
                                </span>
                                <span class="hero-duration">2 ชม. 4 นาที</span>
                                <span class="hero-quality">
                                    <img src="<?php echo get_template_directory_uri(); ?>/assets/images/hd-badge.png" alt="HD" class="quality-badge">
                                </span>
                            </div>
                            <h1 class="hero-title">Jurassic World Rebirth (2025) จูราสสิค เวิลด์ กำเนิดชีวิตใหม่</h1>
                            <p class="hero-description">จูราสสิค เวิลด์ กำเนิดชีวิตใหม่ ยุคใหม่ของไดโนเสาร์ได้เริ่มต้นแล้ว การกลับมาของสายพันธุ์ล้านปี เมื่อเหล่าไดโนเสาร์จะช่วยชีวิตมนุษยชาติได้</p>
                            <div class="hero-buttons">
                                <a href="#" class="btn btn-primary hero-watch-btn">
                                    <i class="fas fa-play"></i> ดูหนัง
                                </a>
                                <a href="#" class="btn btn-secondary hero-list-btn">
                                    <i class="fas fa-bookmark"></i> รายการของฉัน
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Slide 4: Squid Game 3 -->
                <div class="hero-slide" data-bg="https://ezmovie.me/media/cache/strip/202506/block/f51b5a1b5465de177173b92637bd2d26.png">
                    <div class="hero-bg"></div>
                    <div class="hero-overlay"></div>
                    <div class="container">
                        <div class="hero-content">
                            <div class="hero-meta">
                                <span class="hero-year">2025</span>
                                <span class="hero-rating">
                                    <img src="<?php echo get_template_directory_uri(); ?>/assets/images/rate-18+.png" alt="18+" class="rating-badge">
                                </span>
                                <span class="hero-duration">1 ชม. 58 นาที</span>
                                <span class="hero-quality">
                                    <img src="<?php echo get_template_directory_uri(); ?>/assets/images/hd-badge.png" alt="HD" class="quality-badge">
                                </span>
                            </div>
                            <h1 class="hero-title">Squid Game 3 (2025) สควิดเกม เล่นลุ้นตาย 3</h1>
                            <p class="hero-description">กีฮุนกลับเข้าสู่เกมอีกครั้ง เพื่อหยุดยั้งองค์กรลับที่อยู่เบื้องหลัง พร้อมเผชิญหน้ากับศัตรูเก่าอย่าง ฟรอนต์แมน ในเกมรอบใหม่ที่โหดกว่าเดิม</p>
                            <div class="hero-buttons">
                                <a href="#" class="btn btn-primary hero-watch-btn">
                                    <i class="fas fa-play"></i> ดูหนัง
                                </a>
                                <a href="#" class="btn btn-secondary hero-list-btn">
                                    <i class="fas fa-bookmark"></i> รายการของฉัน
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Slide 5: Ironheart -->
                <div class="hero-slide" data-bg="https://ezmovie.me/media/cache/strip/202506/block/406c562c07c0c3e8f169758e20c4cd72.png">
                    <div class="hero-bg"></div>
                    <div class="hero-overlay"></div>
                    <div class="container">
                        <div class="hero-content">
                            <div class="hero-meta">
                                <span class="hero-year">2025</span>
                                <span class="hero-rating">
                                    <img src="<?php echo get_template_directory_uri(); ?>/assets/images/rate-13+.png" alt="13+" class="rating-badge">
                                </span>
                                <span class="hero-duration">2 ชม. 12 นาที</span>
                                <span class="hero-quality">
                                    <img src="<?php echo get_template_directory_uri(); ?>/assets/images/hd-badge.png" alt="HD" class="quality-badge">
                                </span>
                            </div>
                            <h1 class="hero-title">Ironheart (2025)</h1>
                            <p class="hero-description">หลังกลับจากวากานดา ริริ วิลเลียมส์ ตั้งใจสร้างอนาคตใหม่ที่ชิคาโก แต่กลับต้องเผชิญกับศัตรูที่รวมพลังเวทมนตร์และเทคโนโลยีเข้าด้วยกัน</p>
                            <div class="hero-buttons">
                                <a href="#" class="btn btn-primary hero-watch-btn">
                                    <i class="fas fa-play"></i> ดูหนัง
                                </a>
                                <a href="#" class="btn btn-secondary hero-list-btn">
                                    <i class="fas fa-bookmark"></i> รายการของฉัน
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Navigation Dots -->
                <div class="hero-dots">
                    <span class="dot active" data-slide="0"></span>
                    <span class="dot" data-slide="1"></span>
                    <span class="dot" data-slide="2"></span>
                    <span class="dot" data-slide="3"></span>
                    <span class="dot" data-slide="4"></span>
                </div>

                <!-- Navigation Arrows -->
                <button class="hero-nav prev" aria-label="Previous slide">
                    <i class="fas fa-chevron-left"></i>
                </button>
                <button class="hero-nav next" aria-label="Next slide">
                    <i class="fas fa-chevron-right"></i>
                </button>
            </div>
        </section>

        <!-- EZMovie Description Section -->
        <section class="ezmovie-description">
            <div class="container">
                <h2 class="section-title">EZ Movie ดูหนังออนไลน์ฟรี หนังใหม่ 2025 ไม่มีโฆษณา ชัดที่สุด ไวที่สุด</h2>
            </div>
        </section>

        <!-- หนังมาแรง Section -->
        <section class="category-section trending-section">
            <div class="container">
                <div class="section-header">
                    <h2 class="section-title">หนังมาแรง</h2>
                </div>
                <div class="movie-section-slider" id="trending-slider">
                    <button class="movie-nav-arrow prev" data-target="trending" aria-label="Previous">
                        <i class="fas fa-chevron-left"></i>
                    </button>
                    <button class="movie-nav-arrow next" data-target="trending" aria-label="Next">
                        <i class="fas fa-chevron-right"></i>
                    </button>
                    <div class="movies-grid-wrapper">
                        <div class="movies-grid">
                        <?php
                        $movies = array(
                            array('title' => 'Jurassic World Rebirth (2025) จูราสสิค เวิลด์ กำเนิดชีวิตใหม่', 'poster' => 'https://ezmovie.me/media/cache/strip/202507/block/03794c7927a655afde43650456749704.png'),
                            array('title' => 'F1 The Movie (2025) F1 เดอะ มูฟวี่', 'poster' => 'https://ezmovie.me/media/cache/strip/202506/block/f5e774dccc8757022019e5ca13d59535.png'),
                            array('title' => 'How to Train Your Dragon (2025) อภินิหารไวกิ้งพิชิตมังกร', 'poster' => 'https://ezmovie.me/media/cache/strip/202506/block/614d59bbb8e842c302564008053ea1bf.png'),
                            array('title' => '28 Years Later (2025) 28 ปีให้หลัง เชื้อเขมือบคน', 'poster' => 'https://ezmovie.me/media/cache/strip/202506/block/9e1276075341d40a1cfb1d69aaba6259.png'),
                            array('title' => 'Lilo & Stitch (2025) ลีโล & สติทช์', 'poster' => 'https://ezmovie.me/media/cache/strip/202505/block/69bf3ffc002b2f8c341260059cd417e2.png'),
                            array('title' => 'M3GAN 2.0 (2025) เมแกน 2.0', 'poster' => 'https://ezmovie.me/media/cache/strip/202506/block/406c562c07c0c3e8f169758e20c4cd72.png'),
                            array('title' => 'From the World of John Wick Ballerina (2025) จักรวาลของ จอห์น วิค บัลเลรินา แค้นกว่านรก', 'poster' => 'https://ezmovie.me/media/cache/strip/202506/block/9e1276075341d40a1cfb1d69aaba6259.png'),
                            array('title' => 'Karate Kid Legends (2025) คาราเต้ คิด ผนึกพลังตำนานนักสู้', 'poster' => 'https://ezmovie.me/media/cache/strip/202506/block/c9bdf00bef2b3bc52b4778255c34aa65.png'),
                            array('title' => 'Captain America Brave New World (2025) กัปตัน อเมริกา ศึกฮีโร่จักรวาลใหม่', 'poster' => 'https://ezmovie.me/media/cache/strip/202507/block/03794c7927a655afde43650456749704.png'),
                            array('title' => 'The Fantastic Four First Steps (2025) แฟนแทสติก โฟร์ ก้าวแรกสู่จักรวาล', 'poster' => 'https://ezmovie.me/media/cache/strip/202506/block/f5e774dccc8757022019e5ca13d59535.png'),
                            array('title' => 'Superman Legacy (2025) ซูเปอร์แมน มรดกแห่งความหวัง', 'poster' => 'https://ezmovie.me/media/cache/strip/202506/block/614d59bbb8e842c302564008053ea1bf.png'),
                            array('title' => 'Blade (2025) เบลด นักล่าแวมไพร์', 'poster' => 'https://ezmovie.me/media/cache/strip/202506/block/9e1276075341d40a1cfb1d69aaba6259.png'),
                            array('title' => 'Deadpool 4 (2025) เดดพูล 4', 'poster' => 'https://ezmovie.me/media/cache/strip/202505/block/69bf3ffc002b2f8c341260059cd417e2.png'),
                        );
                        foreach ($movies as $index => $movie) : ?>
                            <div class="movie-card <?php echo $index >= 6 ? 'movie-card-hidden' : ''; ?>">
                                <div class="movie-poster">
                                    <a href="#" class="movie-link">
                                        <img src="<?php echo esc_url($movie['poster']); ?>"
                                             alt="<?php echo esc_attr($movie['title']); ?>"
                                             loading="lazy"
                                             class="movie-poster-img">
                                        <div class="movie-overlay">
                                            <div class="play-button">
                                                <i class="fas fa-play"></i>
                                            </div>
                                        </div>
                                    </a>
                                </div>
                                <div class="movie-info">
                                    <h3 class="movie-title">
                                        <a href="#" class="movie-title-link"><?php echo esc_html($movie['title']); ?></a>
                                    </h3>
                                </div>
                            </div>
                        <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Top 10 ประจำสัปดาห์ Section -->
        <section class="top-10-section">
            <div class="container">
                <div class="section-header">
                    <h2 class="section-title">Top 10 ประจำสัปดาห์</h2>
                </div>
                <div class="top-10-slider-container">
                    <div class="top-10-slider">
                        <!-- First Set (1-5) -->
                        <div class="top-10-slide active">
                            <div class="top-10-grid">
                                <?php
                                $top_movies_set1 = array(
                                    array('rank' => 1, 'title' => 'ปากกัด ตีนถีบ (2025) Ziam', 'poster' => 'https://ezmovie.me/media/cache/strip/202507/block/5e81659fc61cb71daf4c578db29b9a2c.png'),
                                    array('rank' => 2, 'title' => 'Squid Game 3 (2025) สควิดเกม เล่นลุ้นตาย 3', 'poster' => 'https://ezmovie.me/media/cache/strip/202506/block/f51b5a1b5465de177173b92637bd2d26.png'),
                                    array('rank' => 3, 'title' => 'The Old Guard 2 (2025) ดิ โอลด์ การ์ด 2', 'poster' => 'https://ezmovie.me/media/cache/strip/202507/block/03794c7927a655afde43650456749704.png'),
                                    array('rank' => 4, 'title' => 'Ironheart (2025)', 'poster' => 'https://ezmovie.me/media/cache/strip/202506/block/406c562c07c0c3e8f169758e20c4cd72.png'),
                                    array('rank' => 5, 'title' => 'Ghost Rider (2007) โกสต์ ไรเดอร์ มัจจุราชแห่งรัตติกาล', 'poster' => 'https://ezmovie.me/media/cache/strip/202506/block/9e1276075341d40a1cfb1d69aaba6259.png'),
                                );
                                foreach ($top_movies_set1 as $movie) : ?>
                                    <div class="top-movie-card">
                                        <div class="rank-number"><?php echo $movie['rank']; ?></div>
                                        <div class="movie-poster">
                                            <a href="#" class="movie-link">
                                                <img src="<?php echo esc_url($movie['poster']); ?>"
                                                     alt="<?php echo esc_attr($movie['title']); ?>"
                                                     loading="lazy"
                                                     class="movie-poster-img">
                                                <div class="movie-overlay">
                                                    <div class="play-button">
                                                        <i class="fas fa-play"></i>
                                                    </div>
                                                </div>
                                            </a>
                                        </div>
                                        <div class="movie-info">
                                            <h3 class="movie-title">
                                                <a href="#" class="movie-title-link"><?php echo esc_html($movie['title']); ?></a>
                                            </h3>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>

                        <!-- Second Set (6-10) -->
                        <div class="top-10-slide">
                            <div class="top-10-grid">
                                <?php
                                $top_movies_set2 = array(
                                    array('rank' => 6, 'title' => 'สงคราม ส่งด่วน (2025) Mad Unicorn', 'poster' => 'https://ezmovie.me/media/cache/strip/202506/block/f5e774dccc8757022019e5ca13d59535.png'),
                                    array('rank' => 7, 'title' => 'Captain America Brave New World (2025) กัปตัน อเมริกา ศึกฮีโร่จักรวาลใหม่', 'poster' => 'https://ezmovie.me/media/cache/strip/202506/block/614d59bbb8e842c302564008053ea1bf.png'),
                                    array('rank' => 8, 'title' => 'Mercy For None (2025) ดับแค้นไร้ปรานี', 'poster' => 'https://ezmovie.me/media/cache/strip/202505/block/69bf3ffc002b2f8c341260059cd417e2.png'),
                                    array('rank' => 9, 'title' => 'The Gorge (2025) มฤตยูใต้โลก', 'poster' => 'https://ezmovie.me/media/cache/strip/202506/block/c9bdf00bef2b3bc52b4778255c34aa65.png'),
                                    array('rank' => 10, 'title' => 'FUBAR 2 (2025)', 'poster' => 'https://ezmovie.me/media/cache/strip/202507/block/03794c7927a655afde43650456749704.png'),
                                );
                                foreach ($top_movies_set2 as $movie) : ?>
                                    <div class="top-movie-card">
                                        <div class="rank-number"><?php echo $movie['rank']; ?></div>
                                        <div class="movie-poster">
                                            <a href="#" class="movie-link">
                                                <img src="<?php echo esc_url($movie['poster']); ?>"
                                                     alt="<?php echo esc_attr($movie['title']); ?>"
                                                     loading="lazy"
                                                     class="movie-poster-img">
                                                <div class="movie-overlay">
                                                    <div class="play-button">
                                                        <i class="fas fa-play"></i>
                                                    </div>
                                                </div>
                                            </a>
                                        </div>
                                        <div class="movie-info">
                                            <h3 class="movie-title">
                                                <a href="#" class="movie-title-link"><?php echo esc_html($movie['title']); ?></a>
                                            </h3>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- หนังมาใหม่ Section -->
        <section class="category-section">
            <div class="container">
                <div class="section-header">
                    <h2 class="section-title">หนังมาใหม่</h2>
                    <a href="#" class="view-all-link">ดูทั้งหมด</a>
                </div>
                <div class="movie-section-slider" id="new-movies-slider">
                    <button class="movie-nav-arrow prev" data-target="new-movies" aria-label="Previous">
                        <i class="fas fa-chevron-left"></i>
                    </button>
                    <button class="movie-nav-arrow next" data-target="new-movies" aria-label="Next">
                        <i class="fas fa-chevron-right"></i>
                    </button>
                    <div class="movies-grid">
                    <?php
                    $new_movies = array(
                        array('title' => 'Nosferatu (2024) นอสเฟอราตู แวมไพร์อมตะ', 'poster' => 'https://ezmovie.me/media/cache/strip/202507/block/03794c7927a655afde43650456749704.png'),
                        array('title' => 'Mufasa The Lion King (2024) มูฟาซา เดอะ ไลอ้อน คิง', 'poster' => 'https://ezmovie.me/media/cache/strip/202506/block/f5e774dccc8757022019e5ca13d59535.png'),
                        array('title' => 'Sonic the Hedgehog 3 (2024) โซนิค เดอะ เฮดจ์ฮอก 3', 'poster' => 'https://ezmovie.me/media/cache/strip/202506/block/614d59bbb8e842c302564008053ea1bf.png'),
                        array('title' => 'Wicked (2024) วิกเก็ด นางฟ้าพิทักษ์โลก', 'poster' => 'https://ezmovie.me/media/cache/strip/202506/block/9e1276075341d40a1cfb1d69aaba6259.png'),
                        array('title' => 'Gladiator II (2024) กลาดิเอเตอร์ 2', 'poster' => 'https://ezmovie.me/media/cache/strip/202505/block/69bf3ffc002b2f8c341260059cd417e2.png'),
                        array('title' => 'Moana 2 (2024) โมอาน่า 2 ผจญภัยใหม่ในมหาสมุทร', 'poster' => 'https://ezmovie.me/media/cache/strip/202506/block/406c562c07c0c3e8f169758e20c4cd72.png'),
                        array('title' => 'Red One (2024) เรด วัน ปฏิบัติการช่วยซานต้า', 'poster' => 'https://ezmovie.me/media/cache/strip/202506/block/f51b5a1b5465de177173b92637bd2d26.png'),
                        array('title' => 'Venom The Last Dance (2024) เวน่อม เดอะ ลาสต์ แดนซ์', 'poster' => 'https://ezmovie.me/media/cache/strip/202507/block/5e81659fc61cb71daf4c578db29b9a2c.png'),
                        array('title' => 'Terrifier 3 (2024) สยองขวัญ 3', 'poster' => 'https://ezmovie.me/media/cache/strip/202506/block/c9bdf00bef2b3bc52b4778255c34aa65.png'),
                        array('title' => 'Smile 2 (2024) สไมล์ 2', 'poster' => 'https://ezmovie.me/media/cache/strip/202507/block/03794c7927a655afde43650456749704.png'),
                        array('title' => 'The Wild Robot (2024) หุ่นยนต์ป่าเถื่อน', 'poster' => 'https://ezmovie.me/media/cache/strip/202506/block/f5e774dccc8757022019e5ca13d59535.png'),
                        array('title' => 'Beetlejuice Beetlejuice (2024) บีเทิลจูซ บีเทิลจูซ', 'poster' => 'https://ezmovie.me/media/cache/strip/202506/block/614d59bbb8e842c302564008053ea1bf.png'),
                        array('title' => 'Deadpool & Wolverine (2024) เดดพูล แอนด์ วูล์ฟเวอรีน', 'poster' => 'https://ezmovie.me/media/cache/strip/202506/block/9e1276075341d40a1cfb1d69aaba6259.png'),
                    );
                    foreach ($new_movies as $movie) : ?>
                        <div class="movie-card">
                            <div class="movie-poster">
                                <a href="#">
                                    <img src="<?php echo esc_url($movie['poster']); ?>" alt="<?php echo esc_attr($movie['title']); ?>" loading="lazy">
                                </a>
                            </div>
                            <div class="movie-info">
                                <h3 class="movie-title">
                                    <a href="#"><?php echo esc_html($movie['title']); ?></a>
                                </h3>
                            </div>
                        </div>
                    <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </section>

        <!-- ซีรี่ย์ยอดนิยม Section -->
        <section class="category-section">
            <div class="container">
                <div class="section-header">
                    <h2 class="section-title">ซีรี่ย์ยอดนิยม</h2>
                    <div class="category-controls">
                        <button class="category-nav prev" data-target="series" aria-label="Previous">
                            <i class="fas fa-chevron-left"></i>
                        </button>
                        <button class="category-nav next" data-target="series" aria-label="Next">
                            <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>
                </div>
                <div class="category-slider" id="series-slider">
                    <div class="movies-grid">
                        <?php
                        $series = array(
                            array('title' => 'House of the Dragon Season 2 (2024) ตระกูลแห่งมังกร', 'poster' => 'https://ezmovie.me/media/cache/strip/202506/block/406c562c07c0c3e8f169758e20c4cd72.png'),
                            array('title' => 'Squid Game 2 (2024) สควิดเกม เล่นลุ้นตาย 2', 'poster' => 'https://ezmovie.me/media/cache/strip/202506/block/f5e774dccc8757022019e5ca13d59535.png'),
                            array('title' => 'Game of Thrones Season 1 พากย์ไทย', 'poster' => 'https://ezmovie.me/media/cache/strip/202506/block/614d59bbb8e842c302564008053ea1bf.png'),
                            array('title' => 'The Witcher Season 3 (2023) เดอะวิทเชอร์ นักล่าจอมอสูร 3', 'poster' => 'https://ezmovie.me/media/cache/strip/202505/block/69bf3ffc002b2f8c341260059cd417e2.png'),
                            array('title' => 'Wednesday Season 1 (2022) เวนส์เดย์', 'poster' => 'https://ezmovie.me/media/cache/strip/202506/block/c9bdf00bef2b3bc52b4778255c34aa65.png'),
                            array('title' => 'Stranger Things Season 4 (2022) สเตรนเจอร์ ธิงส์ 4', 'poster' => 'https://ezmovie.me/media/cache/strip/202507/block/03794c7927a655afde43650456749704.png'),
                            array('title' => 'The Boys Season 4 (2024) เดอะบอยส์ 4', 'poster' => 'https://ezmovie.me/media/cache/strip/202506/block/f51b5a1b5465de177173b92637bd2d26.png'),
                            array('title' => 'Emily in Paris Season 4 (2024) เอมิลี่ในปารีส 4', 'poster' => 'https://ezmovie.me/media/cache/strip/202507/block/5e81659fc61cb71daf4c578db29b9a2c.png'),
                            // เพิ่ม 8 เรื่องสำหรับสไลด์ที่ 2
                            array('title' => 'The Crown Season 6 (2023) เดอะ คราวน์ 6', 'poster' => 'https://ezmovie.me/media/cache/strip/202506/block/9e1276075341d40a1cfb1d69aaba6259.png'),
                            array('title' => 'Bridgerton Season 3 (2024) บริดเจอร์ตัน 3', 'poster' => 'https://ezmovie.me/media/cache/strip/202506/block/f5e774dccc8757022019e5ca13d59535.png'),
                            array('title' => 'The Umbrella Academy Season 4 (2024) ดิ อัมเบรลลา อะคาเดมี่ 4', 'poster' => 'https://ezmovie.me/media/cache/strip/202506/block/614d59bbb8e842c302564008053ea1bf.png'),
                            array('title' => 'Money Heist Korea Season 1 (2022) ทรชนคลั่ง เกาหลี', 'poster' => 'https://ezmovie.me/media/cache/strip/202505/block/69bf3ffc002b2f8c341260059cd417e2.png'),
                            array('title' => 'Ozark Season 4 (2022) โอซาร์ก 4', 'poster' => 'https://ezmovie.me/media/cache/strip/202506/block/c9bdf00bef2b3bc52b4778255c34aa65.png'),
                            array('title' => 'Euphoria Season 2 (2022) ยูโฟเรีย 2', 'poster' => 'https://ezmovie.me/media/cache/strip/202507/block/03794c7927a655afde43650456749704.png'),
                            array('title' => 'The Mandalorian Season 3 (2023) เดอะ แมนดาลอเรียน 3', 'poster' => 'https://ezmovie.me/media/cache/strip/202506/block/f51b5a1b5465de177173b92637bd2d26.png'),
                            array('title' => 'Loki Season 2 (2023) โลกิ 2', 'poster' => 'https://ezmovie.me/media/cache/strip/202507/block/5e81659fc61cb71daf4c578db29b9a2c.png'),
                        );
                        foreach ($series as $movie) : ?>
                            <div class="movie-card">
                                <div class="movie-poster">
                                    <a href="#">
                                        <img src="<?php echo esc_url($movie['poster']); ?>" alt="<?php echo esc_attr($movie['title']); ?>" loading="lazy">
                                    </a>
                                </div>
                                <div class="movie-info">
                                    <h3 class="movie-title">
                                        <a href="#"><?php echo esc_html($movie['title']); ?></a>
                                    </h3>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </section>

        <!-- อนิเมะยอดนิยม Section -->
        <section class="category-section">
            <div class="container">
                <div class="section-header">
                    <h2 class="section-title">อนิเมะยอดนิยม</h2>
                    <div class="category-controls">
                        <button class="category-nav prev" data-target="anime" aria-label="Previous">
                            <i class="fas fa-chevron-left"></i>
                        </button>
                        <button class="category-nav next" data-target="anime" aria-label="Next">
                            <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>
                </div>
                <div class="category-slider" id="anime-slider">
                    <div class="movies-grid">
                        <?php
                        $anime = array(
                            array('title' => 'Demon Slayer Season 5 (2024) ดาบพิฆาตอสูร ภาคการสั่งสอนของเสาหลัก', 'poster' => 'https://ezmovie.me/media/cache/strip/202506/block/406c562c07c0c3e8f169758e20c4cd72.png'),
                            array('title' => 'Attack on Titan The Last Attack (2024) ฝ่าพิภพไททัน การจู่โจมครั้งสุดท้าย', 'poster' => 'https://ezmovie.me/media/cache/strip/202506/block/f5e774dccc8757022019e5ca13d59535.png'),
                            array('title' => 'Dragon Ball Daima (2024) ดราก้อนบอล ไดมะ', 'poster' => 'https://ezmovie.me/media/cache/strip/202506/block/614d59bbb8e842c302564008053ea1bf.png'),
                            array('title' => 'Solo Leveling 2 (2025)', 'poster' => 'https://ezmovie.me/media/cache/strip/202505/block/69bf3ffc002b2f8c341260059cd417e2.png'),
                            array('title' => 'Blue Lock Season 2 (2024) บลู ล็อค ขังดวลแข้ง 2', 'poster' => 'https://ezmovie.me/media/cache/strip/202506/block/c9bdf00bef2b3bc52b4778255c34aa65.png'),
                            array('title' => 'Jujutsu Kaisen Season 3 (2024) ยูจิ อิตาโดริ นักเรียนมัธยมปลาย', 'poster' => 'https://ezmovie.me/media/cache/strip/202507/block/03794c7927a655afde43650456749704.png'),
                            array('title' => 'One Piece (2024) วันพีช', 'poster' => 'https://ezmovie.me/media/cache/strip/202506/block/f51b5a1b5465de177173b92637bd2d26.png'),
                            array('title' => 'Naruto Shippuden (2024) นารูโตะ ชิปปูเดน', 'poster' => 'https://ezmovie.me/media/cache/strip/202507/block/5e81659fc61cb71daf4c578db29b9a2c.png'),
                            // เพิ่ม 8 เรื่องสำหรับสไลด์ที่ 2
                            array('title' => 'My Hero Academia Season 7 (2024) มายฮีโร่ อคาเดเมีย 7', 'poster' => 'https://ezmovie.me/media/cache/strip/202506/block/9e1276075341d40a1cfb1d69aaba6259.png'),
                            array('title' => 'Tokyo Revengers Season 3 (2024) โตเกียว รีเวนเจอร์ส 3', 'poster' => 'https://ezmovie.me/media/cache/strip/202506/block/f5e774dccc8757022019e5ca13d59535.png'),
                            array('title' => 'Chainsaw Man Season 2 (2024) เชนซอว์แมน 2', 'poster' => 'https://ezmovie.me/media/cache/strip/202506/block/614d59bbb8e842c302564008053ea1bf.png'),
                            array('title' => 'Spy x Family Season 2 (2024) สปาย แฟมิลี่ 2', 'poster' => 'https://ezmovie.me/media/cache/strip/202505/block/69bf3ffc002b2f8c341260059cd417e2.png'),
                            array('title' => 'Mob Psycho 100 Season 3 (2024) มอบ ไซโค 100 ซีซั่น 3', 'poster' => 'https://ezmovie.me/media/cache/strip/202506/block/c9bdf00bef2b3bc52b4778255c34aa65.png'),
                            array('title' => 'Hunter x Hunter (2024) ฮันเตอร์ x ฮันเตอร์', 'poster' => 'https://ezmovie.me/media/cache/strip/202507/block/03794c7927a655afde43650456749704.png'),
                            array('title' => 'Bleach TYBW Season 3 (2024) บลีช สงครามเลือดพันปี 3', 'poster' => 'https://ezmovie.me/media/cache/strip/202506/block/f51b5a1b5465de177173b92637bd2d26.png'),
                            array('title' => 'Fullmetal Alchemist (2024) แฟลเมทัล อัลเคมิสต์', 'poster' => 'https://ezmovie.me/media/cache/strip/202507/block/5e81659fc61cb71daf4c578db29b9a2c.png'),
                        );
                        foreach ($anime as $movie) : ?>
                            <div class="movie-card">
                                <div class="movie-poster">
                                    <a href="#">
                                        <img src="<?php echo esc_url($movie['poster']); ?>" alt="<?php echo esc_attr($movie['title']); ?>" loading="lazy">
                                    </a>
                                </div>
                                <div class="movie-info">
                                    <h3 class="movie-title">
                                        <a href="#"><?php echo esc_html($movie['title']); ?></a>
                                    </h3>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </section>

    <?php endif; ?>

</main>

<?php get_footer(); ?>
