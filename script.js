// Movie Data
const movieData = {
    recommended: [
        {
            title: "Superman (2025)",
            image: "https://ezmovie.me/media/cache/strip/202507/block/eacf81bbc9a770e457bb16b7004a92e5.png"
        },
        {
            title: "ปากกัด ตีนถีบ (2025)",
            image: "https://ezmovie.me/media/cache/strip/202507/block/5e81659fc61cb71daf4c578db29b9a2c.png"
        },
        {
            title: "Jurassic World Rebirth (2025)",
            image: "https://ezmovie.me/media/cache/strip/202507/block/03794c7927a655afde43650456749704.png"
        },
        {
            title: "Squid Game 3 (2025)",
            image: "https://ezmovie.me/media/cache/strip/202506/block/f51b5a1b5465de177173b92637bd2d26.png"
        },
        {
            title: "Ironheart (2025)",
            image: "https://ezmovie.me/media/cache/strip/202506/block/406c562c07c0c3e8f169758e20c4cd72.png"
        },
        {
            title: "F1 The Movie (2025)",
            image: "https://ezmovie.me/media/cache/strip/202506/block/f5e774dccc8757022019e5ca13d59535.png"
        },
        {
            title: "How to Train Your Dragon (2025)",
            image: "https://ezmovie.me/media/cache/strip/202506/block/614d59bbb8e842c302564008053ea1bf.png"
        },
        {
            title: "28 Years Later (2025)",
            image: "https://ezmovie.me/media/cache/strip/202506/block/9e1276075341d40a1cfb1d69aaba6259.png"
        }
    ],
    newMovies: [
        {
            title: "Lilo & Stitch (2025)",
            image: "https://ezmovie.me/media/cache/strip/202505/block/69bf3ffc002b2f8c341260059cd417e2.png"
        },
        {
            title: "M3GAN 2.0 (2025)",
            image: "https://ezmovie.me/media/cache/strip/202506/block/406c562c07c0c3e8f169758e20c4cd72.png"
        },
        {
            title: "Ballerina (2025)",
            image: "https://ezmovie.me/media/cache/strip/202506/block/9e1276075341d40a1cfb1d69aaba6259.png"
        },
        {
            title: "Karate Kid Legends (2025)",
            image: "https://ezmovie.me/media/cache/strip/202506/block/c9bdf00bef2b3bc52b4778255c34aa65.png"
        },
        {
            title: "Captain America (2025)",
            image: "https://ezmovie.me/media/cache/strip/202507/block/03794c7927a655afde43650456749704.png"
        },
        {
            title: "Fantastic Four (2025)",
            image: "https://ezmovie.me/media/cache/strip/202506/block/f5e774dccc8757022019e5ca13d59535.png"
        },
        {
            title: "Blade (2025)",
            image: "https://ezmovie.me/media/cache/strip/202506/block/9e1276075341d40a1cfb1d69aaba6259.png"
        },
        {
            title: "Deadpool 4 (2025)",
            image: "https://ezmovie.me/media/cache/strip/202505/block/69bf3ffc002b2f8c341260059cd417e2.png"
        }
    ],
    top10: [
        {
            title: "ปากกัด ตีนถีบ (2025)",
            image: "https://ezmovie.me/media/cache/strip/202507/block/5e81659fc61cb71daf4c578db29b9a2c.png",
            rank: 1
        },
        {
            title: "Squid Game 3 (2025)",
            image: "https://ezmovie.me/media/cache/strip/202506/block/f51b5a1b5465de177173b92637bd2d26.png",
            rank: 2
        },
        {
            title: "The Old Guard 2 (2025)",
            image: "https://ezmovie.me/media/cache/strip/202507/block/03794c7927a655afde43650456749704.png",
            rank: 3
        },
        {
            title: "Ironheart (2025)",
            image: "https://ezmovie.me/media/cache/strip/202506/block/406c562c07c0c3e8f169758e20c4cd72.png",
            rank: 4
        },
        {
            title: "Ghost Rider (2007)",
            image: "https://ezmovie.me/media/cache/strip/202506/block/9e1276075341d40a1cfb1d69aaba6259.png",
            rank: 5
        },
        {
            title: "สงคราม ส่งด่วน (2025)",
            image: "https://ezmovie.me/media/cache/strip/202506/block/f5e774dccc8757022019e5ca13d59535.png",
            rank: 6
        },
        {
            title: "Captain America (2025)",
            image: "https://ezmovie.me/media/cache/strip/202506/block/614d59bbb8e842c302564008053ea1bf.png",
            rank: 7
        },
        {
            title: "Mercy For None (2025)",
            image: "https://ezmovie.me/media/cache/strip/202505/block/69bf3ffc002b2f8c341260059cd417e2.png",
            rank: 8
        },
        {
            title: "The Gorge (2025)",
            image: "https://ezmovie.me/media/cache/strip/202506/block/c9bdf00bef2b3bc52b4778255c34aa65.png",
            rank: 9
        },
        {
            title: "FUBAR 2 (2025)",
            image: "https://ezmovie.me/media/cache/strip/202507/block/03794c7927a655afde43650456749704.png",
            rank: 10
        }
    ]
};

// DOM Elements
const navbar = document.getElementById('navbar');
const tabButtons = document.querySelectorAll('.tab-btn');
const recommendedContainer = document.getElementById('recommended-movies');
const newMoviesContainer = document.getElementById('new-movies');
const top10Container = document.getElementById('top-10-movies');

// Navbar Scroll Effect
window.addEventListener('scroll', () => {
    if (window.scrollY > 100) {
        navbar.classList.add('scrolled');
    } else {
        navbar.classList.remove('scrolled');
    }
});

// Tab Switching
tabButtons.forEach(button => {
    button.addEventListener('click', () => {
        // Remove active class from all buttons
        tabButtons.forEach(btn => btn.classList.remove('active'));
        // Add active class to clicked button
        button.classList.add('active');
        
        // Here you can add logic to filter content based on selected tab
        const tabType = button.getAttribute('data-tab');
        console.log('Selected tab:', tabType);
    });
});

// Create Movie Card
function createMovieCard(movie, isTop10 = false) {
    const card = document.createElement('div');
    card.className = isTop10 ? 'movie-card top-10-card' : 'movie-card';
    
    card.innerHTML = `
        <img src="${movie.image}" alt="${movie.title}" loading="lazy">
        <div class="movie-overlay">
            <div class="movie-play-icon">
                <i class="fas fa-play"></i>
            </div>
        </div>
        <div class="zoom-tag">ZOOM</div>
        ${isTop10 ? `<div class="rank-number">${movie.rank}</div>` : ''}
    `;
    
    return card;
}

// Populate Movie Sections
function populateMovieSections() {
    // Recommended Movies
    movieData.recommended.forEach(movie => {
        const card = createMovieCard(movie);
        recommendedContainer.appendChild(card);
    });
    
    // New Movies
    movieData.newMovies.forEach(movie => {
        const card = createMovieCard(movie);
        newMoviesContainer.appendChild(card);
    });
    
    // Top 10 Movies
    movieData.top10.forEach(movie => {
        const card = createMovieCard(movie, true);
        top10Container.appendChild(card);
    });
}

// Carousel Navigation
function setupCarouselNavigation() {
    const carousels = document.querySelectorAll('.carousel-container');
    
    carousels.forEach(carousel => {
        const wrapper = carousel.querySelector('.movie-cards-wrapper');
        const leftArrow = carousel.querySelector('.nav-arrow-left');
        const rightArrow = carousel.querySelector('.nav-arrow-right');
        
        if (leftArrow && rightArrow && wrapper) {
            leftArrow.addEventListener('click', () => {
                wrapper.scrollBy({
                    left: -300,
                    behavior: 'smooth'
                });
            });
            
            rightArrow.addEventListener('click', () => {
                wrapper.scrollBy({
                    left: 300,
                    behavior: 'smooth'
                });
            });
        }
    });
}

// Smooth Scroll for Internal Links
function setupSmoothScroll() {
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
}

// Intersection Observer for Animations
function setupScrollAnimations() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-on-scroll');
            }
        });
    }, observerOptions);
    
    // Observe movie sections
    document.querySelectorAll('.movie-section').forEach(section => {
        observer.observe(section);
    });
}

// Loading Animation for Images
function setupImageLoading() {
    const images = document.querySelectorAll('.movie-card img');
    
    images.forEach(img => {
        img.addEventListener('load', () => {
            img.parentElement.classList.remove('loading');
        });
        
        // Add loading class initially
        img.parentElement.classList.add('loading');
    });
}

// Initialize Everything
document.addEventListener('DOMContentLoaded', () => {
    populateMovieSections();
    setupCarouselNavigation();
    setupSmoothScroll();
    setupScrollAnimations();
    setupImageLoading();
    
    console.log('EZmovie website initialized successfully!');
});

// Keyboard Navigation
document.addEventListener('keydown', (e) => {
    // ESC key to close any modals or overlays
    if (e.key === 'Escape') {
        // Add logic to close modals if any
        console.log('Escape key pressed');
    }
    
    // Arrow keys for carousel navigation
    if (e.key === 'ArrowLeft' || e.key === 'ArrowRight') {
        const focusedCarousel = document.querySelector('.carousel-container:hover');
        if (focusedCarousel) {
            const wrapper = focusedCarousel.querySelector('.movie-cards-wrapper');
            if (wrapper) {
                const scrollAmount = e.key === 'ArrowLeft' ? -300 : 300;
                wrapper.scrollBy({
                    left: scrollAmount,
                    behavior: 'smooth'
                });
            }
        }
    }
});

// Touch/Swipe Support for Mobile
let touchStartX = 0;
let touchEndX = 0;

document.addEventListener('touchstart', (e) => {
    touchStartX = e.changedTouches[0].screenX;
});

document.addEventListener('touchend', (e) => {
    touchEndX = e.changedTouches[0].screenX;
    handleSwipe();
});

function handleSwipe() {
    const swipeThreshold = 50;
    const diff = touchStartX - touchEndX;
    
    if (Math.abs(diff) > swipeThreshold) {
        const activeCarousel = document.querySelector('.carousel-container:hover') || 
                              document.querySelector('.carousel-container');
        
        if (activeCarousel) {
            const wrapper = activeCarousel.querySelector('.movie-cards-wrapper');
            if (wrapper) {
                const scrollAmount = diff > 0 ? 300 : -300;
                wrapper.scrollBy({
                    left: scrollAmount,
                    behavior: 'smooth'
                });
            }
        }
    }
}
