/*
Theme Name: DoMovie Theme
Description: EZMovie.me inspired WordPress theme for movie streaming websites. Features dark theme, responsive design, and comprehensive movie management system.
Author: DoMovie Team
Version: 1.0.0
License: GPL v2 or later
License URI: https://www.gnu.org/licenses/gpl-2.0.html
Text Domain: doomovie-theme
Domain Path: /languages
Tags: dark, movies, streaming, responsive, custom-post-types, entertainment
Requires at least: 5.0
Tested up to: 6.4
Requires PHP: 7.4

DoMovie Theme, Copyright 2025
DoMovie Theme is distributed under the terms of the GNU GPL.

This program is free software: you can redistribute it and/or modify
it under the terms of the GNU General Public License as published by
the Free Software Foundation, either version 2 of the License, or
(at your option) any later version.

This program is distributed in the hope that it will be useful,
but WITHOUT ANY WARRANTY; without even the implied warranty of
MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
GNU General Public License for more details.
*/

/* ==========================================================================
   CSS Reset & Base Styles
   ========================================================================== */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    font-size: 16px;
    scroll-behavior: smooth;
}

body {
    font-family: 'Noto Sans Thai', 'Helvetica Neue', Arial, sans-serif;
    line-height: 1.6;
    color: #ffffff;
    background-color: #0a0a0a;
    overflow-x: hidden;
}

/* ==========================================================================
   CSS Variables (Dark Theme)
   ========================================================================== */

:root {
    /* Primary Colors */
    --primary-black: #000000;
    --primary-red: #e50914;
    --primary-gold: #ffd700;
    
    /* Background Colors */
    --bg-dark: #141414;
    --bg-darker: #0a0a0a;
    --bg-card: #1a1a1a;
    --bg-overlay: rgba(0, 0, 0, 0.8);
    
    /* Text Colors */
    --text-white: #ffffff;
    --text-gray: #b3b3b3;
    --text-light-gray: #8c8c8c;
    --text-dark-gray: #666666;
    
    /* Accent Colors */
    --accent-red: #e50914;
    --accent-hover: #f40612;
    --accent-gold: #ffd700;
    
    /* Border Colors */
    --border-gray: #333333;
    --border-light: #555555;
    
    /* Font Sizes */
    --font-size-xs: 12px;
    --font-size-sm: 14px;
    --font-size-base: 16px;
    --font-size-lg: 18px;
    --font-size-xl: 24px;
    --font-size-2xl: 32px;
    --font-size-3xl: 48px;
    
    /* Spacing */
    --spacing-xs: 4px;
    --spacing-sm: 8px;
    --spacing-md: 16px;
    --spacing-lg: 24px;
    --spacing-xl: 32px;
    --spacing-2xl: 48px;
    
    /* Border Radius */
    --radius-sm: 4px;
    --radius-md: 8px;
    --radius-lg: 12px;
    --radius-xl: 16px;
    
    /* Shadows */
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.3);
    --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.4);
    --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.5);
    
    /* Transitions */
    --transition-fast: 0.2s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;
    
    /* Z-index */
    --z-dropdown: 1000;
    --z-sticky: 1020;
    --z-fixed: 1030;
    --z-modal-backdrop: 1040;
    --z-modal: 1050;
    --z-popover: 1060;
    --z-tooltip: 1070;
}

/* ==========================================================================
   Typography
   ========================================================================== */

h1, h2, h3, h4, h5, h6 {
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: var(--spacing-md);
    color: var(--text-white);
}

h1 { font-size: var(--font-size-3xl); }
h2 { font-size: var(--font-size-2xl); }
h3 { font-size: var(--font-size-xl); }
h4 { font-size: var(--font-size-lg); }
h5 { font-size: var(--font-size-base); }
h6 { font-size: var(--font-size-sm); }

p {
    margin-bottom: var(--spacing-md);
    color: var(--text-gray);
}

a {
    color: var(--accent-red);
    text-decoration: none;
    transition: color var(--transition-fast);
}

a:hover {
    color: var(--accent-hover);
}

/* ==========================================================================
   Layout & Container
   ========================================================================== */

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-md);
}

.container-fluid {
    width: 100%;
    padding: 0 var(--spacing-md);
}

.row {
    display: flex;
    flex-wrap: wrap;
    margin: 0 calc(var(--spacing-md) * -0.5);
}

.col {
    flex: 1;
    padding: 0 calc(var(--spacing-md) * 0.5);
}

/* ==========================================================================
   Grid System
   ========================================================================== */

.movie-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: var(--spacing-lg);
    margin: var(--spacing-xl) 0;
}

@media (max-width: 576px) {
    .movie-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-md);
    }
}

@media (min-width: 577px) and (max-width: 768px) {
    .movie-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (min-width: 769px) and (max-width: 992px) {
    .movie-grid {
        grid-template-columns: repeat(4, 1fr);
    }
}

@media (min-width: 993px) and (max-width: 1200px) {
    .movie-grid {
        grid-template-columns: repeat(5, 1fr);
    }
}

@media (min-width: 1201px) {
    .movie-grid {
        grid-template-columns: repeat(6, 1fr);
    }
}

/* ==========================================================================
   Buttons
   ========================================================================== */

.btn {
    display: inline-block;
    padding: var(--spacing-sm) var(--spacing-lg);
    font-size: var(--font-size-base);
    font-weight: 600;
    text-align: center;
    text-decoration: none;
    border: none;
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all var(--transition-fast);
    line-height: 1.5;
}

.btn-primary {
    background-color: var(--accent-red);
    color: var(--text-white);
}

.btn-primary:hover {
    background-color: var(--accent-hover);
    color: var(--text-white);
}

.btn-secondary {
    background-color: transparent;
    color: var(--text-white);
    border: 2px solid var(--border-gray);
}

.btn-secondary:hover {
    background-color: var(--text-white);
    color: var(--bg-dark);
    border-color: var(--text-white);
}

.btn-sm {
    padding: var(--spacing-xs) var(--spacing-md);
    font-size: var(--font-size-sm);
}

.btn-lg {
    padding: var(--spacing-md) var(--spacing-xl);
    font-size: var(--font-size-lg);
}

/* ==========================================================================
   Header Styles - EZMovie Style
   ========================================================================== */

.site-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    background: rgba(10, 10, 10, 0.9);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    padding: 15px 0;
}

.site-header.scrolled {
    background: #000000;
    box-shadow: 0 2px 8px rgba(0,0,0,0.3);
    padding: 10px 0;
}

.header-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.site-branding .logo-text {
    color: #e50914;
    font-size: 28px;
    font-weight: 800;
    letter-spacing: -1px;
    text-decoration: none;
}

.main-navigation .nav-menu {
    display: flex;
    list-style: none;
    gap: 30px;
    margin: 0;
    padding: 0;
}

.nav-link {
    color: #b3b3b3;
    text-decoration: none;
    font-weight: 500;
    transition: 0.3s ease;
    position: relative;
    padding: 8px 0;
}

.nav-link:hover,
.nav-link.active {
    color: #ffffff;
}

.nav-link.active::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: #e50914;
    border-radius: 2px;
}

.header-actions {
    display: flex;
    gap: 15px;
}

.nav-icon-btn {
    background: none;
    border: none;
    color: #b3b3b3;
    font-size: 18px;
    cursor: pointer;
    padding: 8px;
    border-radius: 50%;
    transition: 0.3s ease;
}

.nav-icon-btn:hover {
    color: #ffffff;
    background: rgba(255, 255, 255, 0.1);
}

/* ==========================================================================
   Hero Section - EZMovie Style
   ========================================================================== */

.hero {
    position: relative;
    height: 100vh;
    min-height: 600px;
    display: flex;
    align-items: center;
    overflow: hidden;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -2;
}

.hero-background img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    filter: brightness(0.4) blur(1px);
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to right, rgba(0,0,0,0.8) 0%, rgba(0,0,0,0.4) 50%, rgba(0,0,0,0.1) 100%);
    z-index: -1;
}

.hero-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    z-index: 1;
}

.hero-info {
    max-width: 600px;
}

.hero-title {
    font-size: clamp(2.5rem, 5vw, 4rem);
    font-weight: 800;
    margin-bottom: 20px;
    line-height: 1.1;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
}

.hero-description {
    font-size: 18px;
    color: #b3b3b3;
    margin-bottom: 25px;
    line-height: 1.6;
    max-width: 500px;
}

.hero-tags {
    display: flex;
    gap: 10px;
    margin-bottom: 30px;
    flex-wrap: wrap;
}

.tag {
    background: rgba(229, 9, 20, 0.2);
    color: #e50914;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 500;
    border: 1px solid #e50914;
}

.play-btn {
    background: #e50914;
    color: #ffffff;
    border: none;
    padding: 15px 30px;
    border-radius: 12px;
    font-size: 18px;
    font-weight: 600;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 12px;
    transition: 0.3s ease;
    box-shadow: 0 2px 8px rgba(0,0,0,0.3);
}

.play-btn:hover {
    background: #f40612;
    transform: translateY(-3px);
    box-shadow: 0 8px 32px rgba(0,0,0,0.6);
}

.play-btn i {
    font-size: 16px;
}

/* Tab Categories */
.tab-categories {
    background: #000000;
    padding: 20px 0;
    border-bottom: 1px solid #333333;
}

.tabs {
    display: flex;
    gap: 30px;
    justify-content: center;
    flex-wrap: wrap;
}

.tab-btn {
    background: none;
    border: none;
    color: #b3b3b3;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    padding: 12px 0;
    position: relative;
    transition: 0.3s ease;
}

.tab-btn:hover,
.tab-btn.active {
    color: #ffffff;
}

.tab-btn.active::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: #e50914;
    border-radius: 2px;
}

/* Main Content */
.main-content {
    padding: 60px 0;
}

.movie-section {
    margin-bottom: 60px;
}

.section-header {
    margin-bottom: 30px;
}

.section-title {
    font-size: 28px;
    font-weight: 700;
    color: #ffffff;
}

/* Carousel Container */
.carousel-container {
    position: relative;
}

.movie-cards-wrapper {
    display: flex;
    gap: 20px;
    overflow-x: auto;
    scroll-behavior: smooth;
    padding: 10px 0;
    scrollbar-width: none;
    -ms-overflow-style: none;
}

.movie-cards-wrapper::-webkit-scrollbar {
    display: none;
}

/* Navigation Arrows */
.nav-arrow {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(0, 0, 0, 0.7);
    border: none;
    color: #ffffff;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    cursor: pointer;
    font-size: 18px;
    transition: 0.3s ease;
    z-index: 10;
    backdrop-filter: blur(10px);
}

.nav-arrow:hover {
    background: #e50914;
    transform: translateY(-50%) scale(1.1);
}

.nav-arrow-left {
    left: -25px;
}

.nav-arrow-right {
    right: -25px;
}

/* Movie Cards */
.movie-card {
    position: relative;
    min-width: 200px;
    height: 300px;
    border-radius: 12px;
    overflow: hidden;
    cursor: pointer;
    transition: 0.5s ease;
    background: #1a1a1a;
}

.movie-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 8px 32px rgba(0,0,0,0.6);
}

.movie-card img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: 0.5s ease;
}

.movie-card:hover img {
    transform: scale(1.05);
}

.movie-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: 0.3s ease;
}

.movie-card:hover .movie-overlay {
    opacity: 1;
}

.movie-play-icon {
    width: 60px;
    height: 60px;
    background: #e50914;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #ffffff;
    font-size: 24px;
    transition: 0.3s ease;
}

.movie-play-icon:hover {
    background: #f40612;
    transform: scale(1.1);
}

.zoom-tag {
    position: absolute;
    top: 10px;
    left: 10px;
    background: #00ff00;
    color: #000;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 700;
    z-index: 5;
}

/* Top 10 Specific Styles */
.top-10-wrapper .movie-card {
    min-width: 250px;
    height: 350px;
}

.top-10-card {
    position: relative;
}

.rank-number {
    position: absolute;
    top: -10px;
    left: -10px;
    font-size: 120px;
    font-weight: 900;
    color: rgba(255, 255, 255, 0.1);
    -webkit-text-stroke: 2px #e50914;
    z-index: 3;
    line-height: 1;
    pointer-events: none;
}

/* Footer */
.footer {
    background: #000000;
    padding: 40px 0;
    text-align: center;
    border-top: 1px solid #333333;
}

.view-all-btn {
    background: linear-gradient(45deg, #e50914, #ff6b6b);
    color: #ffffff;
    border: none;
    padding: 15px 30px;
    border-radius: 12px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 10px;
    transition: 0.3s ease;
    box-shadow: 0 2px 8px rgba(0,0,0,0.3);
}

.view-all-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 32px rgba(0,0,0,0.6);
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-menu {
        display: none;
    }

    .hero-title {
        font-size: 2rem;
    }

    .hero-description {
        font-size: 16px;
    }

    .tabs {
        gap: 15px;
    }

    .tab-btn {
        font-size: 14px;
    }

    .movie-card {
        min-width: 150px;
        height: 225px;
    }

    .top-10-wrapper .movie-card {
        min-width: 180px;
        height: 270px;
    }

    .nav-arrow {
        display: none;
    }

    .rank-number {
        font-size: 80px;
    }

    .section-title {
        font-size: 20px;
    }

    .main-content {
        padding: 40px 0;
    }

    .movie-section {
        margin-bottom: 40px;
    }
}

/* ==========================================================================
   Movie Detail Page Styles
   ========================================================================== */

.movie-detail-section {
    position: relative;
    min-height: 100vh;
    display: flex;
    align-items: center;
    overflow: hidden;
    margin-top: 70px;
}

.movie-detail-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -2;
}

.movie-detail-background img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    filter: brightness(0.3) blur(2px);
}

.movie-detail-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to right, rgba(0,0,0,0.9) 0%, rgba(0,0,0,0.6) 50%, rgba(0,0,0,0.3) 100%);
    z-index: -1;
}

.movie-detail-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 60px;
    align-items: center;
    padding: 60px 0;
}

.movie-detail-info {
    max-width: 600px;
}

.movie-detail-title {
    font-size: clamp(2rem, 4vw, 3.5rem);
    font-weight: 800;
    margin-bottom: 20px;
    line-height: 1.1;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
}

.movie-meta {
    display: flex;
    align-items: center;
    gap: 20px;
    margin-bottom: 25px;
    flex-wrap: wrap;
}

.movie-year {
    color: #b3b3b3;
    font-weight: 600;
    font-size: 16px;
}

.rating-badge,
.quality-badge {
    height: 24px;
    width: auto;
}

.movie-duration {
    color: #b3b3b3;
    font-weight: 500;
}

.movie-description {
    font-size: 18px;
    color: #b3b3b3;
    line-height: 1.6;
    margin-bottom: 25px;
}

.movie-tags {
    display: flex;
    gap: 10px;
    margin-bottom: 40px;
    flex-wrap: wrap;
}

.movie-buttons {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

.play-btn-large {
    background: #e50914;
    color: #ffffff;
    border: none;
    padding: 18px 35px;
    border-radius: 12px;
    font-size: 18px;
    font-weight: 600;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 12px;
    transition: 0.3s ease;
    box-shadow: 0 4px 15px rgba(229, 9, 20, 0.4);
}

.play-btn-large:hover {
    background: #f40612;
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(229, 9, 20, 0.6);
}

.trailer-btn,
.favorite-btn {
    background: rgba(255, 255, 255, 0.1);
    color: #ffffff;
    border: 2px solid rgba(255, 255, 255, 0.3);
    padding: 16px 30px;
    border-radius: 12px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 10px;
    transition: 0.3s ease;
    backdrop-filter: blur(10px);
}

.trailer-btn:hover,
.favorite-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-2px);
}

.movie-poster-large {
    text-align: center;
}

.movie-poster-large img {
    max-width: 100%;
    height: auto;
    border-radius: 15px;
    box-shadow: 0 10px 40px rgba(0,0,0,0.6);
    transition: 0.3s ease;
}

.movie-poster-large img:hover {
    transform: scale(1.05);
}

/* Movie Player */
.movie-player-section {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.95);
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
}

.player-wrapper {
    position: relative;
    width: 90%;
    max-width: 1200px;
    aspect-ratio: 16/9;
}

.video-player {
    width: 100%;
    height: 100%;
    border-radius: 15px;
    overflow: hidden;
}

.video-player iframe {
    width: 100%;
    height: 100%;
}

.close-player {
    position: absolute;
    top: -50px;
    right: 0;
    background: #e50914;
    color: #ffffff;
    border: none;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    cursor: pointer;
    font-size: 18px;
    transition: 0.3s ease;
}

.close-player:hover {
    background: #f40612;
    transform: scale(1.1);
}

/* Movie Info Section */
.movie-info-section {
    background: #0a0a0a;
    padding: 60px 0;
}

.movie-info-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
}

.movie-details h3,
.movie-synopsis h3 {
    font-size: 24px;
    font-weight: 700;
    margin-bottom: 25px;
    color: #ffffff;
}

.detail-item {
    margin-bottom: 15px;
    font-size: 16px;
    line-height: 1.6;
}

.detail-item strong {
    color: #ffffff;
    font-weight: 600;
    display: inline-block;
    min-width: 120px;
}

.detail-item span {
    color: #b3b3b3;
}

.movie-synopsis p {
    font-size: 16px;
    line-height: 1.8;
    color: #b3b3b3;
    margin-bottom: 20px;
}

/* Related Movies Section */
.related-movies-section {
    background: #000000;
    padding: 60px 0;
}

.related-movies-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 30px;
    margin-top: 30px;
}

.related-movie-card {
    position: relative;
    border-radius: 12px;
    overflow: hidden;
    transition: 0.3s ease;
    background: #1a1a1a;
}

.related-movie-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 40px rgba(0,0,0,0.6);
}

.related-movie-card img {
    width: 100%;
    height: 300px;
    object-fit: cover;
    transition: 0.3s ease;
}

.related-movie-card:hover img {
    transform: scale(1.05);
}

.related-movie-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: 0.3s ease;
}

.related-movie-card:hover .related-movie-overlay {
    opacity: 1;
}

.related-play-icon {
    width: 50px;
    height: 50px;
    background: #e50914;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #ffffff;
    font-size: 20px;
    transition: 0.3s ease;
}

.related-play-icon:hover {
    background: #f40612;
    transform: scale(1.1);
}

.related-movie-card h4 {
    padding: 15px;
    font-size: 14px;
    font-weight: 600;
    color: #ffffff;
    text-align: center;
    margin: 0;
}

.related-movie-card a {
    text-decoration: none;
    color: inherit;
}

/* Responsive for Movie Detail Page */
@media (max-width: 768px) {
    .movie-detail-content {
        grid-template-columns: 1fr;
        gap: 40px;
        text-align: center;
    }

    .movie-detail-title {
        font-size: 2rem;
    }

    .movie-buttons {
        justify-content: center;
        flex-direction: column;
        align-items: center;
    }

    .play-btn-large,
    .trailer-btn,
    .favorite-btn {
        width: 100%;
        max-width: 300px;
        justify-content: center;
    }

    .movie-info-grid {
        grid-template-columns: 1fr;
        gap: 40px;
    }

    .related-movies-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 20px;
    }

    .player-wrapper {
        width: 95%;
    }

    .close-player {
        top: -40px;
        right: 10px;
    }
}

/* Search Form */
.search-form-container {
    background: var(--bg-dark);
    border-bottom: 1px solid var(--border-gray);
    padding: var(--spacing-lg) 0;
}

.search-form-wrapper {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.search-input-group {
    flex: 1;
    display: flex;
    background: var(--bg-card);
    border-radius: var(--radius-lg);
    overflow: hidden;
}

.search-field {
    flex: 1;
    padding: var(--spacing-md) var(--spacing-lg);
    border: none;
    background: transparent;
    color: var(--text-white);
    font-size: var(--font-size-base);
}

.search-field::placeholder {
    color: var(--text-gray);
}

.search-submit {
    padding: var(--spacing-md) var(--spacing-lg);
    background: var(--accent-red);
    border: none;
    color: var(--text-white);
    cursor: pointer;
    transition: background-color var(--transition-fast);
}

.search-submit:hover {
    background: var(--accent-hover);
}

.search-close {
    background: none;
    border: none;
    color: var(--text-gray);
    font-size: 20px;
    padding: var(--spacing-sm);
    cursor: pointer;
    transition: color var(--transition-fast);
}

.search-close:hover {
    color: var(--text-white);
}

.form-control:focus {
    outline: none;
    border-color: var(--accent-red);
}

.form-control::placeholder {
    color: var(--text-dark-gray);
}

/* ==========================================================================
   Utilities
   ========================================================================== */

.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.d-none { display: none; }
.d-block { display: block; }
.d-inline { display: inline; }
.d-inline-block { display: inline-block; }
.d-flex { display: flex; }
.d-grid { display: grid; }

.justify-content-center { justify-content: center; }
.justify-content-between { justify-content: space-between; }
.justify-content-around { justify-content: space-around; }

.align-items-center { align-items: center; }
.align-items-start { align-items: flex-start; }
.align-items-end { align-items: flex-end; }

.flex-wrap { flex-wrap: wrap; }
.flex-nowrap { flex-wrap: nowrap; }

.w-100 { width: 100%; }
.h-100 { height: 100%; }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: var(--spacing-xs); }
.mb-2 { margin-bottom: var(--spacing-sm); }
.mb-3 { margin-bottom: var(--spacing-md); }
.mb-4 { margin-bottom: var(--spacing-lg); }
.mb-5 { margin-bottom: var(--spacing-xl); }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: var(--spacing-xs); }
.mt-2 { margin-top: var(--spacing-sm); }
.mt-3 { margin-top: var(--spacing-md); }
.mt-4 { margin-top: var(--spacing-lg); }
.mt-5 { margin-top: var(--spacing-xl); }

/* ==========================================================================
   Responsive Utilities
   ========================================================================== */

@media (max-width: 576px) {
    .d-sm-none { display: none; }
    .d-sm-block { display: block; }
    .text-sm-center { text-align: center; }
}

@media (max-width: 768px) {
    .d-md-none { display: none; }
    .d-md-block { display: block; }
    .text-md-center { text-align: center; }
}

@media (max-width: 992px) {
    .d-lg-none { display: none; }
    .d-lg-block { display: block; }
    .text-lg-center { text-align: center; }
}

.hero-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        to right,
        rgba(0, 0, 0, 0.8) 0%,
        rgba(0, 0, 0, 0.4) 50%,
        rgba(0, 0, 0, 0.1) 100%
    );
}

.hero-content {
    position: relative;
    z-index: 2;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    max-width: 600px;
    padding: var(--spacing-2xl) 0;
}

.hero-meta {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-md);
}

.hero-year {
    color: var(--text-gray);
    font-weight: 600;
}

.rating-badge,
.quality-badge {
    height: 20px;
    width: auto;
}

.hero-duration {
    color: var(--text-gray);
    font-weight: 500;
}

.hero-title {
    font-size: clamp(2rem, 5vw, 3.5rem);
    font-weight: 800;
    color: var(--text-white);
    margin-bottom: var(--spacing-lg);
    line-height: 1.1;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
}

.hero-description {
    font-size: var(--font-size-lg);
    color: var(--text-gray);
    line-height: 1.6;
    margin-bottom: var(--spacing-2xl);
    max-width: 500px;
}

.hero-buttons {
    display: flex;
    gap: var(--spacing-md);
    flex-wrap: wrap;
}

.hero-watch-btn {
    background: var(--accent-red);
    color: var(--text-white);
    padding: var(--spacing-md) var(--spacing-2xl);
    font-size: var(--font-size-lg);
    font-weight: 600;
    border-radius: var(--radius-md);
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    transition: all var(--transition-fast);
}

.hero-watch-btn:hover {
    background: var(--accent-hover);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.hero-list-btn {
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-white);
    border: 2px solid rgba(255, 255, 255, 0.3);
    padding: var(--spacing-md) var(--spacing-2xl);
    font-size: var(--font-size-lg);
    font-weight: 600;
    border-radius: var(--radius-md);
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    transition: all var(--transition-fast);
    backdrop-filter: blur(10px);
}

.hero-list-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-2px);
}

/* Hero Navigation */
.hero-dots {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: var(--spacing-sm);
    z-index: 3;
}

.hero-dots .dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.4);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.hero-dots .dot.active {
    background: var(--accent-red);
    transform: scale(1.2);
}

.hero-nav {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(0, 0, 0, 0.5);
    border: none;
    color: var(--text-white);
    width: 50px;
    height: 50px;
    border-radius: 50%;
    cursor: pointer;
    font-size: 18px;
    transition: all var(--transition-fast);
    z-index: 3;
    backdrop-filter: blur(10px);
}

.hero-nav:hover {
    background: rgba(229, 9, 20, 0.8);
    transform: translateY(-50%) scale(1.1);
}

.hero-nav.prev {
    left: 30px;
}

.hero-nav.next {
    right: 30px;
}

/* EZMovie Description */
.ezmovie-description {
    background: var(--bg-dark);
    padding: var(--spacing-2xl) 0;
    text-align: center;
}

.ezmovie-description .section-title {
    font-size: clamp(1.5rem, 4vw, 2.5rem);
    font-weight: 700;
    color: var(--text-white);
    margin: 0;
}

/* ==========================================================================
   Loading & Animations
   ========================================================================== */

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideInUp {
    from {
        transform: translateY(30px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.fade-in {
    animation: fadeIn var(--transition-normal);
}

.slide-in-up {
    animation: slideInUp var(--transition-normal);
}

.loading {
    position: relative;
    overflow: hidden;
}

.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* ==========================================================================
   Movie Sections - EZMovie Style
   ========================================================================== */

.category-section {
    padding: var(--spacing-2xl) 0;
    background: var(--bg-darker);
}

.section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-2xl);
}

.section-title {
    font-size: clamp(1.5rem, 3vw, 2rem);
    font-weight: 700;
    color: var(--text-white);
    margin: 0;
}

.view-all-link {
    color: var(--accent-red);
    text-decoration: none;
    font-weight: 600;
    transition: color var(--transition-fast);
}

.view-all-link:hover {
    color: var(--accent-hover);
}

.movie-section-slider {
    position: relative;
}

.movies-grid-wrapper {
    overflow: hidden;
    border-radius: var(--radius-lg);
}

.movies-grid {
    display: grid;
    grid-template-columns: repeat(13, 1fr);
    gap: var(--spacing-md);
    transition: transform 0.5s ease;
}

.movie-card {
    position: relative;
    border-radius: var(--radius-lg);
    overflow: hidden;
    background: var(--bg-card);
    transition: all var(--transition-fast);
}

.movie-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.movie-card-hidden {
    display: none;
}

.movie-poster {
    position: relative;
    aspect-ratio: 2/3;
    overflow: hidden;
}

.movie-poster-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--transition-fast);
}

.movie-card:hover .movie-poster-img {
    transform: scale(1.05);
}

.movie-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity var(--transition-fast);
}

.movie-card:hover .movie-overlay {
    opacity: 1;
}

.play-button {
    width: 60px;
    height: 60px;
    background: var(--accent-red);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-white);
    font-size: 24px;
    transition: all var(--transition-fast);
}

.play-button:hover {
    background: var(--accent-hover);
    transform: scale(1.1);
}

.movie-info {
    padding: var(--spacing-md);
}

.movie-title-link {
    color: var(--text-white);
    text-decoration: none;
    font-weight: 600;
    font-size: var(--font-size-sm);
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    transition: color var(--transition-fast);
}

.movie-title-link:hover {
    color: var(--accent-red);
}

/* Movie Navigation Arrows */
.movie-nav-arrow {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(0, 0, 0, 0.7);
    border: none;
    color: var(--text-white);
    width: 50px;
    height: 50px;
    border-radius: 50%;
    cursor: pointer;
    font-size: 18px;
    transition: all var(--transition-fast);
    z-index: 2;
    backdrop-filter: blur(10px);
}

.movie-nav-arrow:hover {
    background: var(--accent-red);
    transform: translateY(-50%) scale(1.1);
}

.movie-nav-arrow:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.movie-nav-arrow.prev {
    left: -25px;
}

.movie-nav-arrow.next {
    right: -25px;
}

/* ==========================================================================
   Top 10 Section - EZMovie Style
   ========================================================================== */

.top-10-section {
    padding: var(--spacing-2xl) 0;
    background: var(--bg-dark);
}

.top-10-slider-container {
    position: relative;
    overflow: hidden;
}

.top-10-slider {
    display: flex;
    transition: transform 0.5s ease;
}

.top-10-slide {
    min-width: 100%;
    opacity: 0;
    transition: opacity 0.5s ease;
}

.top-10-slide.active {
    opacity: 1;
}

.top-10-grid {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: var(--spacing-lg);
}

.top-movie-card {
    position: relative;
    border-radius: var(--radius-lg);
    overflow: hidden;
    background: var(--bg-card);
    transition: all var(--transition-fast);
}

.top-movie-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.rank-number {
    position: absolute;
    top: var(--spacing-sm);
    left: var(--spacing-sm);
    background: var(--accent-red);
    color: var(--text-white);
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: var(--font-size-sm);
    z-index: 2;
    box-shadow: var(--shadow-md);
}

/* Auto-sliding animation for Top 10 */
@keyframes top10AutoSlide {
    0%, 45% { transform: translateX(0); }
    50%, 95% { transform: translateX(-100%); }
    100% { transform: translateX(0); }
}

.top-10-slider {
    animation: top10AutoSlide 10s infinite;
}

.top-10-slider:hover {
    animation-play-state: paused;
}

/* ==========================================================================
   Responsive Design - EZMovie Style
   ========================================================================== */

/* Mobile First Approach */
@media (max-width: 768px) {
    .header-content {
        padding: var(--spacing-sm) 0;
        min-height: 60px;
    }

    .hero-section {
        margin-top: 60px;
        min-height: 500px;
    }

    .hero-content {
        padding: var(--spacing-lg) 0;
        max-width: 100%;
    }

    .hero-title {
        font-size: clamp(1.5rem, 6vw, 2.5rem);
    }

    .hero-description {
        font-size: var(--font-size-base);
        margin-bottom: var(--spacing-lg);
    }

    .hero-buttons {
        flex-direction: column;
        gap: var(--spacing-sm);
    }

    .hero-watch-btn,
    .hero-list-btn {
        padding: var(--spacing-sm) var(--spacing-lg);
        font-size: var(--font-size-base);
        justify-content: center;
    }

    .hero-nav {
        width: 40px;
        height: 40px;
        font-size: 16px;
    }

    .hero-nav.prev {
        left: 15px;
    }

    .hero-nav.next {
        right: 15px;
    }

    .main-navigation {
        display: none; /* Hide main nav on mobile, would need mobile menu */
    }

    .sponsor-links {
        display: none; /* Hide sponsors on mobile */
    }

    .movies-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-sm);
    }

    .top-10-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-md);
    }

    .movie-nav-arrow {
        display: none; /* Hide arrows on mobile */
    }

    .section-title {
        font-size: clamp(1.2rem, 4vw, 1.5rem);
    }

    .category-section {
        padding: var(--spacing-lg) 0;
    }
}

/* Tablet */
@media (min-width: 769px) and (max-width: 1024px) {
    .movies-grid {
        grid-template-columns: repeat(4, 1fr);
    }

    .top-10-grid {
        grid-template-columns: repeat(3, 1fr);
    }

    .hero-content {
        max-width: 500px;
    }
}

/* Desktop */
@media (min-width: 1025px) {
    .movies-grid {
        grid-template-columns: repeat(6, 1fr);
    }

    .top-10-grid {
        grid-template-columns: repeat(5, 1fr);
    }
}

/* Large Desktop */
@media (min-width: 1400px) {
    .container {
        max-width: 1320px;
    }

    .movies-grid {
        grid-template-columns: repeat(7, 1fr);
    }
}

/* ==========================================================================
   Interactive Features & Animations
   ========================================================================== */

/* Loading states */
.movie-card.loading {
    opacity: 0.7;
}

.movie-card.loading .movie-poster-img {
    filter: blur(2px);
}

.movie-card.loaded {
    opacity: 1;
    transition: opacity 0.3s ease;
}

.movie-card.loaded .movie-poster-img {
    filter: none;
    transition: filter 0.3s ease;
}

/* Enhanced hover states */
.movie-card.hovered {
    z-index: 10;
}

.movie-card.hovered .movie-poster-img {
    transform: scale(1.05);
}

/* Back to top button */
.back-to-top {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 50px;
    height: 50px;
    background: var(--accent-red);
    color: var(--text-white);
    border: none;
    border-radius: 50%;
    cursor: pointer;
    font-size: 18px;
    transition: all var(--transition-fast);
    z-index: var(--z-fixed);
    display: none;
    box-shadow: var(--shadow-lg);
}

.back-to-top:hover {
    background: var(--accent-hover);
    transform: scale(1.1);
}

/* Footer styles */
.footer-bottom {
    background: var(--bg-darker);
    padding: var(--spacing-2xl) 0;
    border-top: 1px solid var(--border-gray);
}

.footer-bottom-content {
    text-align: center;
}

.footer-logo {
    margin-bottom: var(--spacing-xl);
}

.footer-logo-img {
    height: 60px;
    width: auto;
}

.footer-sponsors {
    display: flex;
    justify-content: center;
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

.footer-sponsors .sponsor-link img {
    height: 40px;
    width: auto;
    transition: transform var(--transition-fast);
}

.footer-sponsors .sponsor-link:hover img {
    transform: scale(1.1);
}

.footer-links {
    margin-bottom: var(--spacing-xl);
}

.footer-menu {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: var(--spacing-md);
    list-style: none;
    margin: 0 0 var(--spacing-lg) 0;
    padding: 0;
}

.footer-menu a {
    color: var(--text-gray);
    text-decoration: none;
    font-size: var(--font-size-sm);
    transition: color var(--transition-fast);
}

.footer-menu a:hover {
    color: var(--accent-red);
}

.footer-links-bottom {
    display: flex;
    justify-content: center;
    gap: var(--spacing-lg);
    flex-wrap: wrap;
}

.footer-links-bottom a {
    color: var(--text-gray);
    text-decoration: none;
    font-weight: 600;
    transition: color var(--transition-fast);
}

.footer-links-bottom a:hover {
    color: var(--accent-red);
}

.footer-copyright {
    color: var(--text-gray);
    font-size: var(--font-size-sm);
}

/* Movie request section */
.movie-request-section {
    background: linear-gradient(135deg, var(--accent-red), #c41e3a);
    padding: var(--spacing-2xl) 0;
    text-align: center;
}

.request-content h2 {
    color: var(--text-white);
    font-size: clamp(1.5rem, 4vw, 2.5rem);
    margin-bottom: var(--spacing-md);
}

.request-content p {
    color: rgba(255, 255, 255, 0.9);
    font-size: var(--font-size-lg);
    margin-bottom: var(--spacing-xl);
}

.request-movie-btn {
    background: var(--text-white);
    color: var(--accent-red);
    padding: var(--spacing-md) var(--spacing-2xl);
    font-weight: 700;
    border-radius: var(--radius-lg);
    text-decoration: none;
    display: inline-block;
    transition: all var(--transition-fast);
}

.request-movie-btn:hover {
    background: rgba(255, 255, 255, 0.9);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

/* ==========================================================================
   Print Styles
   ========================================================================== */

@media print {
    * {
        background: transparent !important;
        color: black !important;
        box-shadow: none !important;
        text-shadow: none !important;
    }
    
    a, a:visited {
        text-decoration: underline;
    }
    
    .no-print {
        display: none !important;
    }
}

/* ==========================================================================
   Hero Section - EZMovie Full Width Style
   ========================================================================== */

.hero-section {
    position: relative;
    width: 100vw;
    height: 70vh;
    min-height: 500px;
    margin-left: calc(-50vw + 50%);
    overflow: hidden;
    background: var(--bg-darker);
}

.hero-slider {
    position: relative;
    width: 100%;
    height: 100%;
}

.hero-slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    visibility: hidden;
    transition: all 1.2s cubic-bezier(0.4, 0, 0.2, 1);
    transform: scale(1.05);
}

.hero-slide.active {
    opacity: 1;
    visibility: visible;
    transform: scale(1);
}

.hero-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    background-attachment: fixed;
    filter: brightness(0.7) contrast(1.1);
    transition: all 1.5s ease;
}

.hero-slide.active .hero-bg {
    filter: brightness(0.8) contrast(1.2);
    transform: scale(1.02);
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        135deg,
        rgba(0,0,0,0.9) 0%,
        rgba(0,0,0,0.6) 40%,
        rgba(0,0,0,0.3) 70%,
        rgba(0,0,0,0.7) 100%
    );
    z-index: 1;
}

.hero-content {
    position: relative;
    z-index: 3;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    max-width: 700px;
    padding: var(--spacing-2xl) 0;
    animation: heroContentSlideIn 1.5s ease-out;
}

@keyframes heroContentSlideIn {
    0% {
        opacity: 0;
        transform: translateY(50px) translateX(-30px);
    }
    100% {
        opacity: 1;
        transform: translateY(0) translateX(0);
    }
}

.hero-title {
    font-size: clamp(2rem, 5vw, 4rem);
    font-weight: 800;
    color: var(--text-white);
    margin-bottom: var(--spacing-lg);
    text-shadow:
        2px 2px 4px rgba(0,0,0,0.8),
        0 0 20px rgba(229, 9, 20, 0.3);
    line-height: 1.1;
    letter-spacing: -0.02em;
    background: linear-gradient(135deg, #ffffff 0%, #f0f0f0 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-description {
    font-size: clamp(1rem, 2vw, 1.3rem);
    color: rgba(255,255,255,0.95);
    margin-bottom: var(--spacing-2xl);
    line-height: 1.7;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.7);
    max-width: 600px;
}

.hero-buttons {
    display: flex;
    gap: var(--spacing-lg);
    align-items: center;
}

.hero-buttons .btn {
    padding: 15px 30px;
    font-size: 1.1rem;
    font-weight: 600;
    border-radius: 50px;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    position: relative;
    overflow: hidden;
}

.hero-buttons .btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.6s ease;
}

.hero-buttons .btn:hover::before {
    left: 100%;
}

.hero-buttons .btn-primary {
    background: linear-gradient(135deg, var(--accent-red) 0%, #c41e3a 100%);
    color: white;
    border: none;
    box-shadow: 0 8px 25px rgba(229, 9, 20, 0.4);
}

.hero-buttons .btn-primary:hover {
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 15px 40px rgba(229, 9, 20, 0.6);
    background: linear-gradient(135deg, #ff1744 0%, var(--accent-red) 100%);
}

.hero-buttons .btn-secondary {
    background: rgba(255,255,255,0.1);
    color: white;
    border: 2px solid rgba(255,255,255,0.3);
    backdrop-filter: blur(10px);
}

.hero-buttons .btn-secondary:hover {
    background: rgba(255,255,255,0.2);
    border-color: rgba(255,255,255,0.6);
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 15px 40px rgba(255,255,255,0.1);
}

/* Hero Navigation */
.hero-nav {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(0,0,0,0.5);
    border: none;
    color: white;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
    z-index: 4;
    backdrop-filter: blur(10px);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
}

.hero-nav:hover {
    background: rgba(229, 9, 20, 0.8);
    transform: translateY(-50%) scale(1.1);
    box-shadow: 0 8px 25px rgba(229, 9, 20, 0.4);
}

.hero-nav.prev {
    left: 30px;
}

.hero-nav.next {
    right: 30px;
}

/* Hero Dots */
.hero-dots {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 15px;
    z-index: 4;
}

.hero-dots .dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: rgba(255,255,255,0.4);
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.hero-dots .dot::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 0;
    height: 0;
    border-radius: 50%;
    background: var(--accent-red);
    transition: all 0.3s ease;
}

.hero-dots .dot.active,
.hero-dots .dot:hover {
    background: rgba(255,255,255,0.8);
}

.hero-dots .dot.active::before {
    width: 8px;
    height: 8px;
}

/* Auto-loading background images */
.hero-slide[data-bg] .hero-bg {
    background-image: var(--bg-image);
}

/* Dynamic background loading - Updated with larger images */
.hero-slide[data-bg="https://ezmovie.me/media/cache/strip/202507/block/5ee62f102163415175cd6c62fc4abe9d.jpg"] .hero-bg {
    background-image: url('https://ezmovie.me/media/cache/strip/202507/block/5ee62f102163415175cd6c62fc4abe9d.jpg');
}

.hero-slide[data-bg="https://ezmovie.me/media/cache/strip/202507/block/6d0676c157964b9b5d85df0713ec78a2.jpg"] .hero-bg {
    background-image: url('https://ezmovie.me/media/cache/strip/202507/block/6d0676c157964b9b5d85df0713ec78a2.jpg');
}

.hero-slide[data-bg="https://ezmovie.me/media/cache/strip/202506/block/f5eafd128f69d66ff0ca7fdd51a0e4a2.jpg"] .hero-bg {
    background-image: url('https://ezmovie.me/media/cache/strip/202506/block/f5eafd128f69d66ff0ca7fdd51a0e4a2.jpg');
}

.hero-slide[data-bg="https://ezmovie.me/media/cache/strip/202506/block/f5e774dccc8757022019e5ca13d59535.png"] .hero-bg {
    background-image: url('https://ezmovie.me/media/cache/strip/202506/block/f5e774dccc8757022019e5ca13d59535.png');
}

.hero-slide[data-bg="https://ezmovie.me/media/cache/strip/202506/block/614d59bbb8e842c302564008053ea1bf.png"] .hero-bg {
    background-image: url('https://ezmovie.me/media/cache/strip/202506/block/614d59bbb8e842c302564008053ea1bf.png');
}

/* ==========================================================================
   Movie Grid - EZMovie 8 Column Layout (Larger Size)
   ========================================================================== */

.movies-grid {
    display: grid;
    grid-template-columns: repeat(6, 1fr) 0.5fr;
    gap: 20px;
    margin: 40px 0;
    padding: 0 60px;
    position: relative;
    overflow: hidden;
}

.top-10-auto-slider {
    position: relative;
    overflow: hidden;
    margin: 40px 0;
}

.top-10-auto-slider .top-10-grid {
    display: grid;
    grid-template-columns: repeat(8, 1fr);
    gap: 20px;
    padding: 0 20px;
    animation: autoSlide 20s infinite;
}

@keyframes autoSlide {
    0%, 25% { transform: translateX(0); }
    50%, 75% { transform: translateX(-50%); }
    100% { transform: translateX(0); }
}

.top-10-auto-slider:hover .top-10-grid {
    animation-play-state: paused;
}

.top-10-controls {
    display: flex;
    gap: 10px;
}

.top-10-nav {
    background: rgba(0,0,0,0.5);
    border: none;
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
}

.top-10-nav:hover {
    background: var(--accent-red);
    transform: scale(1.1);
}

/* ==========================================================================
   Movie Section Slider System - 6.5 Column Layout
   ========================================================================== */

.movie-section-slider {
    position: relative;
    overflow: hidden;
    margin: 40px 0;
}

.movie-section-slider .movies-grid {
    display: grid;
    grid-template-columns: repeat(12, 1fr) 0.5fr;
    gap: 20px;
    padding: 0 60px;
    transition: transform 0.5s ease;
    width: 200%;
}

.movie-nav-arrow {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(0,0,0,0.7);
    border: none;
    color: white;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    z-index: 10;
    backdrop-filter: blur(10px);
    border: 2px solid rgba(255,255,255,0.1);
}

.movie-nav-arrow:hover {
    background: var(--accent-red);
    transform: translateY(-50%) scale(1.1);
    box-shadow: 0 4px 20px rgba(229, 9, 20, 0.5);
}

.movie-nav-arrow.prev {
    left: 20px;
}

.movie-nav-arrow.next {
    right: 20px;
}

.movie-nav-arrow:disabled {
    opacity: 0.3;
    cursor: not-allowed;
    transform: translateY(-50%);
}

.movie-nav-arrow:disabled:hover {
    background: rgba(0,0,0,0.7);
    transform: translateY(-50%);
    box-shadow: none;
}

.movie-card, .top-movie-card {
    background: linear-gradient(145deg, var(--bg-card) 0%, rgba(26, 26, 26, 0.8) 100%);
    border-radius: 12px;
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid rgba(255,255,255,0.05);
    position: relative;
    cursor: pointer;
    aspect-ratio: 249/351;
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
    width: 100%;
    height: auto;
    min-height: 350px;
}

.movie-card::before, .top-movie-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(229, 9, 20, 0.1) 0%, transparent 50%, rgba(229, 9, 20, 0.05) 100%);
    opacity: 0;
    transition: opacity 0.4s ease;
    z-index: 1;
    pointer-events: none;
}

.movie-card:hover::before, .top-movie-card:hover::before {
    opacity: 1;
}

.movie-card:hover, .top-movie-card:hover {
    transform: translateY(-12px) scale(1.03) rotateX(5deg);
    box-shadow:
        0 25px 50px rgba(229, 9, 20, 0.25),
        0 15px 35px rgba(0,0,0,0.3),
        0 0 0 1px rgba(229, 9, 20, 0.3);
    border-color: rgba(229, 9, 20, 0.5);
}

.movie-poster {
    position: relative;
    width: 100%;
    height: 75%;
    overflow: hidden;
    background: var(--bg-darker);
}

.movie-poster::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(180deg, transparent 0%, transparent 60%, rgba(0,0,0,0.8) 100%);
    opacity: 0;
    transition: opacity 0.4s ease;
    z-index: 2;
}

.movie-card:hover .movie-poster::after,
.top-movie-card:hover .movie-poster::after {
    opacity: 1;
}

.movie-poster img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
    filter: brightness(0.9) contrast(1.1);
}

.movie-card:hover .movie-poster img,
.top-movie-card:hover .movie-poster img {
    transform: scale(1.08) rotate(1deg);
    filter: brightness(1.1) contrast(1.2) saturate(1.1);
}

/* Glowing effect on hover */
.movie-card:hover, .top-movie-card:hover {
    animation: movieCardGlow 2s ease-in-out infinite alternate;
}

@keyframes movieCardGlow {
    0% {
        box-shadow:
            0 25px 50px rgba(229, 9, 20, 0.25),
            0 15px 35px rgba(0,0,0,0.3),
            0 0 0 1px rgba(229, 9, 20, 0.3);
    }
    100% {
        box-shadow:
            0 25px 50px rgba(229, 9, 20, 0.4),
            0 15px 35px rgba(0,0,0,0.3),
            0 0 0 1px rgba(229, 9, 20, 0.6),
            0 0 20px rgba(229, 9, 20, 0.3);
    }
}

.movie-info {
    padding: var(--spacing-sm);
    height: 25%;
    display: flex;
    align-items: center;
}

.movie-title {
    font-size: clamp(0.75rem, 1.5vw, 0.9rem);
    font-weight: 600;
    color: var(--text-white);
    margin: 0;
    line-height: 1.3;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    text-shadow: 0 1px 2px rgba(0,0,0,0.5);
}

.movie-title a {
    color: rgba(255,255,255,0.95);
    text-decoration: none;
    transition: all 0.3s ease;
    background: linear-gradient(135deg, rgba(255,255,255,0.95) 0%, rgba(240,240,240,0.9) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.movie-title a:hover {
    background: linear-gradient(135deg, var(--accent-red) 0%, #ff4569 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    transform: translateY(-1px);
}

/* ==========================================================================
   Top 10 Section - EZMovie Style
   ========================================================================== */

.top-10-section {
    padding: var(--spacing-2xl) 0;
}

.rank-badge {
    position: absolute;
    top: 8px;
    left: 8px;
    background: linear-gradient(135deg, var(--accent-red) 0%, #c41e3a 50%, #8b0000 100%);
    color: var(--text-white);
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
    font-weight: 800;
    z-index: 3;
    box-shadow:
        0 4px 15px rgba(229, 9, 20, 0.4),
        0 0 0 2px rgba(255,255,255,0.1),
        inset 0 1px 0 rgba(255,255,255,0.2);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    border: 2px solid rgba(255,255,255,0.2);
    text-shadow: 0 1px 2px rgba(0,0,0,0.8);
}

.top-movie-card:hover .rank-badge {
    transform: scale(1.15) rotate(8deg);
    box-shadow:
        0 8px 25px rgba(229, 9, 20, 0.6),
        0 0 0 3px rgba(255,255,255,0.2),
        inset 0 1px 0 rgba(255,255,255,0.3),
        0 0 20px rgba(229, 9, 20, 0.4);
    background: linear-gradient(135deg, #ff1744 0%, var(--accent-red) 50%, #c41e3a 100%);
}

/* ==========================================================================
   Section Headers - EZMovie Style
   ========================================================================== */

.category-section, .top-10-section {
    padding: var(--spacing-2xl) 0;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-xl);
    padding-bottom: var(--spacing-sm);
}

.section-title {
    font-size: clamp(1.5rem, 4vw, 2.5rem);
    font-weight: 700;
    color: var(--text-white);
    margin: 0;
    position: relative;
    background: linear-gradient(135deg, #ffffff 0%, #f0f0f0 50%, #e0e0e0 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
    letter-spacing: -0.02em;
}

.view-all-link {
    color: var(--accent-red);
    text-decoration: none;
    font-size: var(--font-size-base);
    font-weight: 500;
    transition: all var(--transition-fast);
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid var(--accent-red);
    border-radius: var(--radius-sm);
    background: transparent;
}

.view-all-link:hover {
    background: var(--accent-red);
    color: var(--text-white);
    transform: translateY(-2px);
}

/* ==========================================================================
   Responsive Design - EZMovie Style
   ========================================================================== */

@media screen and (min-width: 1200px) and (max-width: 1720px) {
    .movies-grid {
        grid-template-columns: repeat(6, 1fr) 0.5fr;
        gap: 18px;
        padding: 0 60px;
    }

    .movie-section-slider .movies-grid {
        grid-template-columns: repeat(12, 1fr) 0.5fr;
        gap: 18px;
        padding: 0 60px;
    }

    .top-10-auto-slider .top-10-grid {
        grid-template-columns: repeat(6, 1fr);
        gap: 18px;
    }
}

@media (max-width: 1200px) {
    .movies-grid {
        grid-template-columns: repeat(5, 1fr) 0.5fr;
        gap: 15px;
        padding: 0 40px;
    }

    .movie-section-slider .movies-grid {
        grid-template-columns: repeat(10, 1fr) 0.5fr;
        gap: 15px;
        padding: 0 40px;
    }

    .top-10-auto-slider .top-10-grid {
        grid-template-columns: repeat(5, 1fr);
        gap: 15px;
    }
}

@media (max-width: 1024px) {
    .movies-grid {
        grid-template-columns: repeat(4, 1fr) 0.5fr;
        gap: 12px;
        padding: 0 30px;
    }

    .movie-section-slider .movies-grid {
        grid-template-columns: repeat(8, 1fr) 0.5fr;
        gap: 12px;
        padding: 0 30px;
    }

    .top-10-auto-slider .top-10-grid {
        grid-template-columns: repeat(4, 1fr);
        gap: 12px;
    }

    .section-title {
        font-size: var(--font-size-xl);
    }

    .rank-badge {
        width: 22px;
        height: 22px;
        font-size: var(--font-size-xs);
    }

    .hero-section {
        height: 50vh;
        min-height: 350px;
    }

    .hero-title {
        font-size: var(--font-size-2xl);
    }

    .hero-description {
        font-size: var(--font-size-base);
    }
}

@media (max-width: 768px) {
    .movies-grid {
        grid-template-columns: repeat(3, 1fr) 0.5fr;
        gap: 10px;
        padding: 0 20px;
    }

    .movie-section-slider .movies-grid {
        grid-template-columns: repeat(6, 1fr) 0.5fr;
        gap: 10px;
        padding: 0 20px;
    }

    .top-10-auto-slider .top-10-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 10px;
    }

    .movie-title {
        font-size: var(--font-size-xs);
    }

    .section-header {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-sm);
    }

    .section-title {
        font-size: var(--font-size-lg);
    }

    .rank-badge {
        width: 20px;
        height: 20px;
        font-size: 10px;
        top: var(--spacing-xs);
        left: var(--spacing-xs);
    }

    /* Hero Section Mobile */
    .hero-section {
        height: 50vh;
        min-height: 350px;
        margin-left: calc(-50vw + 50%);
    }

    .hero-title {
        font-size: clamp(1.5rem, 6vw, 2.5rem);
        margin-bottom: var(--spacing-sm);
    }

    .hero-description {
        font-size: clamp(0.9rem, 3vw, 1.1rem);
        margin-bottom: var(--spacing-lg);
        line-height: 1.5;
    }

    .hero-buttons {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-sm);
    }

    .hero-buttons .btn {
        padding: 12px 24px;
        font-size: 0.9rem;
        width: auto;
        min-width: 140px;
    }

    .hero-nav {
        width: 45px;
        height: 45px;
        font-size: 1rem;
    }

    .hero-nav.prev {
        left: 15px;
    }

    .hero-nav.next {
        right: 15px;
    }

    .hero-dots {
        bottom: 20px;
        gap: 10px;
    }

    .hero-dots .dot {
        width: 10px;
        height: 10px;
    }

    .hero-dots .dot.active::before {
        width: 6px;
        height: 6px;
    }

    .header-content {
        gap: var(--spacing-md);
    }

    .main-navigation {
        margin-left: var(--spacing-md);
    }

    .nav-menu {
        gap: var(--spacing-md);
    }

    .dropdown-menu {
        min-width: 180px;
        padding: 10px 0;
    }

    .dropdown-menu a {
        padding: 10px 15px;
        font-size: 0.9rem;
    }
}

@media (max-width: 480px) {
    .movies-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: var(--spacing-xs);
    }

    .top-10-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: var(--spacing-xs);
    }

    .movie-info {
        padding: var(--spacing-xs);
    }

    .movie-title {
        font-size: 10px;
    }

    .section-title {
        font-size: var(--font-size-base);
    }

    .rank-badge {
        width: 18px;
        height: 18px;
        font-size: 9px;
        top: 5px;
        left: 5px;
    }

    .nav-menu {
        display: none;
    }

    .header-content {
        flex-direction: column;
        gap: var(--spacing-sm);
    }

    .main-navigation {
        margin-left: 0;
        width: 100%;
    }
}

/* ==========================================================================
   Additional Styles for WordPress
   ========================================================================== */

.site {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

.site-content {
    flex: 1;
}

.site-main {
    background: var(--bg-darker);
    min-height: 100vh;
    position: relative;
    z-index: 1;
}

/* WordPress specific classes */
.screen-reader-text {
    clip: rect(1px, 1px, 1px, 1px);
    position: absolute !important;
    height: 1px;
    width: 1px;
    overflow: hidden;
}

.skip-link {
    left: -9999px;
    position: absolute;
    z-index: 999999999;
    text-decoration: none;
}

.skip-link:focus {
    background-color: var(--text-white);
    color: var(--bg-dark);
    left: 6px;
    line-height: normal;
    padding: 15px 23px 14px;
    text-decoration: none;
    top: 7px;
    z-index: 100000;
}
