<?php get_header(); ?>

<div id="primary" class="content-area">
    <main id="main" class="site-main">
        
        <?php
        // Get movie data from URL parameters
        $movie_title = isset($_GET['title']) ? sanitize_text_field($_GET['title']) : 'ไม่พบชื่อหนัง';
        $movie_image = isset($_GET['image']) ? esc_url($_GET['image']) : '';
        ?>

        <!-- Movie Detail Section -->
        <section class="movie-detail-section">
            <div class="movie-detail-background">
                <?php if ($movie_image): ?>
                    <img src="<?php echo $movie_image; ?>" alt="<?php echo $movie_title; ?>">
                <?php endif; ?>
                <div class="movie-detail-overlay"></div>
            </div>
            
            <div class="container">
                <div class="movie-detail-content">
                    <div class="movie-detail-info">
                        <h1 class="movie-detail-title"><?php echo $movie_title; ?></h1>
                        
                        <div class="movie-meta">
                            <span class="movie-year">2025</span>
                            <span class="movie-rating">
                                <img src="<?php echo get_template_directory_uri(); ?>/assets/images/rate-13+.png" alt="13+" class="rating-badge">
                            </span>
                            <span class="movie-duration">2 ชม. 15 นาที</span>
                            <span class="movie-quality">
                                <img src="<?php echo get_template_directory_uri(); ?>/assets/images/hd-badge.png" alt="HD" class="quality-badge">
                            </span>
                        </div>
                        
                        <p class="movie-description">
                            <?php echo $movie_title; ?> - ภาพยนตร์ที่น่าตื่นเต้นและเต็มไปด้วยแอคชั่น พร้อมด้วยเอฟเฟกต์พิเศษที่น่าทึ่ง 
                            เรื่องราวที่จะพาคุณไปสู่การผจญภัยที่ไม่เหมือนใคร ด้วยนักแสดงมากความสามารถและการกำกับที่ยอดเยี่ยม
                        </p>
                        
                        <div class="movie-tags">
                            <span class="tag">#แอคชั่น</span>
                            <span class="tag">#ผจญภัย</span>
                            <span class="tag">#ไซไฟ</span>
                            <span class="tag">#ดราม่า</span>
                        </div>
                        
                        <div class="movie-buttons">
                            <button class="play-btn-large" onclick="playMovie()">
                                <i class="fas fa-play"></i>
                                <span>ดูหนังเลย</span>
                            </button>
                            <button class="trailer-btn" onclick="playTrailer()">
                                <i class="fas fa-video"></i>
                                <span>ดูตัวอย่าง</span>
                            </button>
                            <button class="favorite-btn" onclick="addToFavorite()">
                                <i class="fas fa-heart"></i>
                                <span>เพิ่มในรายการโปรด</span>
                            </button>
                        </div>
                    </div>
                    
                    <div class="movie-poster-large">
                        <?php if ($movie_image): ?>
                            <img src="<?php echo $movie_image; ?>" alt="<?php echo $movie_title; ?>">
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </section>

        <!-- Movie Player Section -->
        <section class="movie-player-section" id="movie-player" style="display: none;">
            <div class="container">
                <div class="player-wrapper">
                    <div class="video-player">
                        <iframe id="movie-iframe" 
                                src="" 
                                frameborder="0" 
                                allowfullscreen>
                        </iframe>
                    </div>
                    <button class="close-player" onclick="closePlayer()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
        </section>

        <!-- Movie Info Section -->
        <section class="movie-info-section">
            <div class="container">
                <div class="movie-info-grid">
                    <div class="movie-details">
                        <h3>รายละเอียดภาพยนตร์</h3>
                        <div class="detail-item">
                            <strong>ผู้กำกับ:</strong> <span>Christopher Nolan</span>
                        </div>
                        <div class="detail-item">
                            <strong>นักแสดงนำ:</strong> <span>Tom Hardy, Anne Hathaway, Jessica Chastain</span>
                        </div>
                        <div class="detail-item">
                            <strong>ประเภท:</strong> <span>แอคชั่น, ผจญภัย, ไซไฟ</span>
                        </div>
                        <div class="detail-item">
                            <strong>ประเทศ:</strong> <span>สหรัฐอเมริกา</span>
                        </div>
                        <div class="detail-item">
                            <strong>ภาษา:</strong> <span>อังกฤษ (พากย์ไทย/ซับไทย)</span>
                        </div>
                        <div class="detail-item">
                            <strong>วันที่เข้าฉาย:</strong> <span>15 มกราคม 2025</span>
                        </div>
                    </div>
                    
                    <div class="movie-synopsis">
                        <h3>เรื่องย่อ</h3>
                        <p>
                            <?php echo $movie_title; ?> เป็นภาพยนตร์ที่เล่าเรื่องราวของการผจญภัยที่น่าตื่นเต้น 
                            ด้วยเทคโนโลยีที่ล้ำสมัยและเอฟเฟกต์พิเศษที่น่าทึ่ง ภาพยนตร์เรื่องนี้จะพาผู้ชมไปสู่โลกใหม่
                            ที่เต็มไปด้วยความลึกลับและการต่อสู้ที่ยิ่งใหญ่
                        </p>
                        <p>
                            เรื่องราวเริ่มต้นขึ้นเมื่อ... (เนื้อหาเรื่องย่อจะถูกเพิ่มตามชื่อหนังที่เลือก)
                            ด้วยการแสดงที่ยอดเยี่ยมของนักแสดงและการกำกับที่มีคุณภาพ ทำให้ภาพยนตร์เรื่องนี้
                            กลายเป็นหนึ่งในภาพยนตร์ที่ไม่ควรพลาด
                        </p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Related Movies Section -->
        <section class="related-movies-section">
            <div class="container">
                <h3 class="section-title">หนังที่เกี่ยวข้อง</h3>
                <div class="related-movies-grid">
                    <!-- Related movies will be populated by JavaScript -->
                </div>
            </div>
        </section>

    </main>
</div>

<script>
// Movie Player Functions
function playMovie() {
    document.getElementById('movie-player').style.display = 'block';
    document.getElementById('movie-iframe').src = 'https://www.youtube.com/embed/dQw4w9WgXcQ?autoplay=1';
    document.body.style.overflow = 'hidden';
}

function playTrailer() {
    document.getElementById('movie-player').style.display = 'block';
    document.getElementById('movie-iframe').src = 'https://www.youtube.com/embed/dQw4w9WgXcQ?autoplay=1';
    document.body.style.overflow = 'hidden';
}

function closePlayer() {
    document.getElementById('movie-player').style.display = 'none';
    document.getElementById('movie-iframe').src = '';
    document.body.style.overflow = 'auto';
}

function addToFavorite() {
    alert('เพิ่มในรายการโปรดเรียบร้อยแล้ว!');
}

// Populate Related Movies
document.addEventListener('DOMContentLoaded', function() {
    var relatedMovies = [
        {title: "Superman (2025)", image: "https://ezmovie.me/media/cache/strip/202507/block/eacf81bbc9a770e457bb16b7004a92e5.png"},
        {title: "Ironheart (2025)", image: "https://ezmovie.me/media/cache/strip/202506/block/406c562c07c0c3e8f169758e20c4cd72.png"},
        {title: "F1 The Movie (2025)", image: "https://ezmovie.me/media/cache/strip/202506/block/f5e774dccc8757022019e5ca13d59535.png"},
        {title: "Squid Game 3 (2025)", image: "https://ezmovie.me/media/cache/strip/202506/block/f51b5a1b5465de177173b92637bd2d26.png"}
    ];
    
    var relatedHtml = '';
    for (var i = 0; i < relatedMovies.length; i++) {
        var movie = relatedMovies[i];
        relatedHtml += '<div class="related-movie-card">';
        relatedHtml += '<a href="movie.php?title=' + encodeURIComponent(movie.title) + '&image=' + encodeURIComponent(movie.image) + '">';
        relatedHtml += '<img src="' + movie.image + '" alt="' + movie.title + '">';
        relatedHtml += '<div class="related-movie-overlay">';
        relatedHtml += '<div class="related-play-icon"><i class="fas fa-play"></i></div>';
        relatedHtml += '</div>';
        relatedHtml += '<h4>' + movie.title + '</h4>';
        relatedHtml += '</a>';
        relatedHtml += '</div>';
    }
    
    document.querySelector('.related-movies-grid').innerHTML = relatedHtml;
});

// Close player on Escape key
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        closePlayer();
    }
});
</script>

<?php get_footer(); ?>
